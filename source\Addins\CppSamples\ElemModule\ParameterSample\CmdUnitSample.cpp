﻿#include "CmdUnitSample.h"
#include "ElemModule.h"
#include "ElemModuleCommandIds.h"
#include "CommandRegister.h"
#include "IDocument.h"
#include "IModelView.h"
#include "UiDocumentViewUtils.h"
#include "IUserTransaction.h"
#include "IApplicationWindow.h"
#include "IApplication.h"
#include "ICommandManager.h"
#include "GcmpCommandNames.h"
#include "IUnitsLibrary.h"
#include "IUnitDimension.h"
#include "UnitConverter.h"
#include "BaseUnit.h"
#include "UnitUniIdentities.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

#define ID_CMD_UNIT_SAMPLE L"CmdUnitSample"

CmdUnitSample::CmdUnitSample()
    :CommandBase(ID_CMD_UNIT_SAMPLE, true)
{
}

/// \brief 声明获取单位定义数据的方法
#define DEFINE_UNIT(UNIT_NAME) \
    ELEMMODULE_EXPORT const gcmp::UniIdentity& GetUnitUid_##UNIT_NAME();

DEFINE_UNIT(TestUnit);
DECLARE_UNIT_GUID(TestUnit) = { 0x30ab7425, 0x6c02, 0x474e,{ 0x9f, 0xfb, 0xd, 0x6b, 0x7, 0x83, 0x1e, 0xc } };
IMPLEMENT_UNIT(TestUnit, GBMP_TR(L"TestUnit"), 1, 0, 1, 0, 1, 0, 1);

gcmp::OwnerPtr<gcmp::IAction> CmdUnitSample::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pDoc, L"pDoc为空", L"GDMPLab", L"2024-12-30");

    OwnerPtr<IUserTransaction> opUserTrans = IUserTransaction::Create(pDoc, L"参数示例");

    // 测试单位量纲
    IUnitsLibrary* pUnitsLib = IUnitsLibrary::Get(pDoc);
    assert(pUnitsLib != nullptr);
    const IUnitDimension* pUnitDim = pUnitsLib->GetUnitDimension(UNIT(TestUnit));
    assert(pUnitDim != nullptr);
    OwnerPtr<IUnitDimension> opUnitDim = IUnitDimension::Create();
    assert(opUnitDim != nullptr);
    opUnitDim->SetExponent(BaseUnit::Length, 1).SetExponent(BaseUnit::Angle, 0).
        SetExponent(BaseUnit::Mass, 1).SetExponent(BaseUnit::Time, 0).SetExponent(BaseUnit::ElectricCurrent, 1).
        SetExponent(BaseUnit::Temperature, 0).SetExponent(BaseUnit::LuminousIntensity, 1);
    assert(opUnitDim->GetExponent(BaseUnit::Length) == 1);
    assert(opUnitDim->GetExponent(BaseUnit::Angle) == 0);
    assert(opUnitDim->GetExponent(BaseUnit::Mass) == 1);
    assert(opUnitDim->GetExponent(BaseUnit::Time) == 0);
    assert(opUnitDim->GetExponent(BaseUnit::ElectricCurrent) == 1);
    assert(opUnitDim->GetExponent(BaseUnit::Temperature) == 0);
    assert(opUnitDim->GetExponent(BaseUnit::LuminousIntensity) == 1);
    assert(pUnitDim->Equals(opUnitDim.get()));

    // 单位转换
    // 速度单位：米每秒(m/s)->千米每小时(km/h)
    IUnitsLibrary* pUnitsLib2 = IUnitsLibrary::Get(pDoc);
    assert(pUnitsLib2 != nullptr);
    OwnerPtr<IUnitDimension> opUnitDim2 = IUnitDimension::Create();
    assert(opUnitDim2 != nullptr);
    opUnitDim2->SetExponent(BaseUnit::Length, 1).SetExponent(BaseUnit::Angle, 0).
        SetExponent(BaseUnit::Mass, 0).SetExponent(BaseUnit::Time, -1).SetExponent(BaseUnit::ElectricCurrent, 0).
        SetExponent(BaseUnit::Temperature, 0).SetExponent(BaseUnit::LuminousIntensity, 0);
    UnitDimensionNames originUnits2, convertedUnits2;
    originUnits2.SetUnits(LengthUnitName::Kilometer, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Hour, CurrentUnitName::UndefinedUnit);
    convertedUnits2.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    double convertdValue2 = UnitConverter::Convert(opUnitDim2.get(), 100.0, originUnits2, convertedUnits2);
    assert(abs(convertdValue2 - 27.77777) <= 0.0001);

    // 加速度单位：(m/s2)->(km/h2)
    opUnitDim2->SetExponent(BaseUnit::Time, -2);
    convertdValue2 = UnitConverter::Convert(opUnitDim2.get(), 100.0, originUnits2, convertedUnits2);
    assert(abs(convertdValue2 - 0.007716) <= 0.0001);

    // 体积单位：立方毫米（mm3)->立方米(m3)
    OwnerPtr<IUnitDimension> opUnitDim3 = IUnitDimension::Create();
    assert(opUnitDim3 != nullptr);
    opUnitDim3->SetExponent(BaseUnit::Length, 3).SetExponent(BaseUnit::Angle, 0).
        SetExponent(BaseUnit::Mass, 0).SetExponent(BaseUnit::Time, 0).SetExponent(BaseUnit::ElectricCurrent, 0).
        SetExponent(BaseUnit::Temperature, 0).SetExponent(BaseUnit::LuminousIntensity, 0);
    UnitDimensionNames originUnits3, convertedUnits3;
    originUnits3.SetUnits(LengthUnitName::Millimeter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit);
    convertedUnits3.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit);
    double convertdValue3 = UnitConverter::Convert(opUnitDim3.get(), 1000.0, originUnits3, convertedUnits3);
    assert(abs(convertdValue3 - 1e-6) <= 0.0001);

    // 长度单位：微米(um)->分米(dm)
    originUnits3.SetUnits(LengthUnitName::Micrometre, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit);
    convertedUnits3.SetUnits(LengthUnitName::Decimetre, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit);
    opUnitDim3->SetExponent(BaseUnit::Length, 1);
    convertdValue3 = UnitConverter::Convert(opUnitDim3.get(), 1000.0, originUnits3, convertedUnits3);
    assert(abs(convertdValue3 - 0.01) <= 0.0001);

    // 面积单位：平方毫米(mm2)->平方米(m2)
    originUnits3.SetUnits(LengthUnitName::Millimeter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit);
    convertedUnits3.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit);
    opUnitDim3->SetExponent(BaseUnit::Length, 2);
    convertdValue3 = UnitConverter::Convert(opUnitDim3.get(), 1000.0, originUnits3, convertedUnits3);
    assert(abs(convertdValue3 - 0.001) <= 0.0001);

    // 热量单位：瓦(kg*m2/s3)->千瓦(T*m2/s3)
    OwnerPtr<IUnitDimension> opUnitDim4 = IUnitDimension::Create();
    assert(opUnitDim4 != nullptr);
    opUnitDim4->SetExponent(BaseUnit::Length, 2).SetExponent(BaseUnit::Angle, 0).
        SetExponent(BaseUnit::Mass, 1).SetExponent(BaseUnit::Time, -3).SetExponent(BaseUnit::ElectricCurrent, 0).
        SetExponent(BaseUnit::Temperature, 0).SetExponent(BaseUnit::LuminousIntensity, 0);
    UnitDimensionNames originUnits4, convertedUnits4;
    originUnits4.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::Kilogram, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    convertedUnits4.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::Ton, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    double convertdValue4 = UnitConverter::Convert(opUnitDim4.get(), 1000.0, originUnits4, convertedUnits4);
    assert(abs(convertdValue4 - 1) <= 0.0001);

    // (g*cm2/s3*mA)->(kg*m2/min3*A)
    opUnitDim4->SetExponent(BaseUnit::Length, 2).SetExponent(BaseUnit::Mass, 1).SetExponent(BaseUnit::Time, -3).
        SetExponent(BaseUnit::ElectricCurrent, -1);
    originUnits4.SetUnits(LengthUnitName::Centimetre, AngleUnitName::UndefinedUnit,
        MassUnitName::Gram, TimeUnitName::Second, CurrentUnitName::Milliampere);
    convertedUnits4.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::Kilogram, TimeUnitName::Minute, CurrentUnitName::Ampere);
    convertdValue4 = UnitConverter::Convert(opUnitDim4.get(), 1.0, originUnits4, convertedUnits4);
    assert(abs(convertdValue4 - 21.6) <= 0.0001);

    // 压力单位：帕斯卡(kg/m*s2)->兆帕(T/mm*s2)
    OwnerPtr<IUnitDimension> opUnitDim5 = IUnitDimension::Create();
    assert(opUnitDim5 != nullptr);
    opUnitDim5->SetExponent(BaseUnit::Length, -1).SetExponent(BaseUnit::Angle, 0).
        SetExponent(BaseUnit::Mass, 1).SetExponent(BaseUnit::Time, -2).SetExponent(BaseUnit::ElectricCurrent, 0).
        SetExponent(BaseUnit::Temperature, 0).SetExponent(BaseUnit::LuminousIntensity, 0);
    UnitDimensionNames originUnits5, convertedUnits5;
    originUnits5.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::Kilogram, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    convertedUnits5.SetUnits(LengthUnitName::Millimeter, AngleUnitName::UndefinedUnit,
        MassUnitName::Ton, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    double convertdValue5 = UnitConverter::Convert(opUnitDim5.get(), 1000.0, originUnits5, convertedUnits5);
    assert(abs(convertdValue5 - 0.001) <= 0.0001);

    // (kg/km*min2)->(mg/cm*s2)
    originUnits5.SetUnits(LengthUnitName::Kilometer, AngleUnitName::UndefinedUnit,
        MassUnitName::Kilogram, TimeUnitName::Minute, CurrentUnitName::UndefinedUnit);
    convertedUnits5.SetUnits(LengthUnitName::Centimetre, AngleUnitName::UndefinedUnit,
        MassUnitName::Milligram, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    convertdValue5 = UnitConverter::Convert(opUnitDim5.get(), 1000.0, originUnits5, convertedUnits5);
    assert(abs(convertdValue5 - 2.77777) <= 0.0001);

    // 比能单位：焦每千克(1m2/s2)->千焦每千克(1000m2/s2)
    OwnerPtr<IUnitDimension> opUnitDim6 = IUnitDimension::Create();
    assert(opUnitDim6 != nullptr);
    opUnitDim6->SetExponent(BaseUnit::Length, 2).SetExponent(BaseUnit::Angle, 0).
        SetExponent(BaseUnit::Mass, 0).SetExponent(BaseUnit::Time, -2).SetExponent(BaseUnit::ElectricCurrent, 0).
        SetExponent(BaseUnit::Temperature, 0).SetExponent(BaseUnit::LuminousIntensity, 0);
    UnitDimensionNames originUnits6, convertedUnits6;
    originUnits6.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    convertedUnits6.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    double convertdValue6 = UnitConverter::Convert(opUnitDim6.get(), 1000.0, originUnits6, convertedUnits6, 1000);
    assert(abs(convertdValue6 - 1) <= 0.0001);

    // 流量单位：立方米每小时(m3/h)->升每秒(3.6m3/h)
    OwnerPtr<IUnitDimension> opUnitDim7 = IUnitDimension::Create();
    assert(opUnitDim7 != nullptr);
    opUnitDim7->SetExponent(BaseUnit::Length, 3).SetExponent(BaseUnit::Angle, 0).
        SetExponent(BaseUnit::Mass, 0).SetExponent(BaseUnit::Time, -1).SetExponent(BaseUnit::ElectricCurrent, 0).
        SetExponent(BaseUnit::Temperature, 0).SetExponent(BaseUnit::LuminousIntensity, 0);
    UnitDimensionNames originUnits7, convertedUnits7;
    originUnits7.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Hour, CurrentUnitName::UndefinedUnit);
    convertedUnits7.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Hour, CurrentUnitName::UndefinedUnit);
    double convertdValue7 = UnitConverter::Convert(opUnitDim7.get(), 1000.0, originUnits7, convertedUnits7, 3.6);
    assert(abs(convertdValue7 - 277.77777) <= 0.0001);

    // 立方米每小时(m3/h)->升每分钟(0.06m3/h)
    originUnits7.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Hour, CurrentUnitName::UndefinedUnit);
    convertedUnits7.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Hour, CurrentUnitName::UndefinedUnit);
    convertdValue7 = UnitConverter::Convert(opUnitDim7.get(), 1000.0, originUnits7, convertedUnits7, 0.06);
    assert(abs(convertdValue7 - 16666.66666) <= 0.0001);

    // (°/s)->(rad/min)
    OwnerPtr<IUnitDimension> opUnitDim8 = IUnitDimension::Create();
    assert(opUnitDim8 != nullptr);
    opUnitDim8->SetExponent(BaseUnit::Length, 0).SetExponent(BaseUnit::Angle, 1).
        SetExponent(BaseUnit::Mass, 0).SetExponent(BaseUnit::Time, -1).SetExponent(BaseUnit::ElectricCurrent, 0).
        SetExponent(BaseUnit::Temperature, 0).SetExponent(BaseUnit::LuminousIntensity, 0);
    UnitDimensionNames originUnits8, convertedUnits8;
    originUnits8.SetUnits(LengthUnitName::UndefinedUnit, AngleUnitName::Angle,
        MassUnitName::UndefinedUnit, TimeUnitName::Second, CurrentUnitName::UndefinedUnit);
    convertedUnits8.SetUnits(LengthUnitName::UndefinedUnit, AngleUnitName::Radian,
        MassUnitName::UndefinedUnit, TimeUnitName::Minute, CurrentUnitName::UndefinedUnit);
    double convertdValue8 = UnitConverter::Convert(opUnitDim8.get(), 100.0, originUnits8, convertedUnits8);
    assert(abs(convertdValue8 - 104.71975) <= 0.0001);

    // 温度单位：摄氏->华氏
    OwnerPtr<IUnitDimension> opUnitDim9 = IUnitDimension::Create();
    assert(opUnitDim9 != nullptr);
    opUnitDim9->SetExponent(BaseUnit::Length, 0).SetExponent(BaseUnit::Angle, 0).
        SetExponent(BaseUnit::Mass, 0).SetExponent(BaseUnit::Time, 0).SetExponent(BaseUnit::ElectricCurrent, 0).
        SetExponent(BaseUnit::Temperature, 1).SetExponent(BaseUnit::LuminousIntensity, 0);
    UnitDimensionNames originUnits9, convertedUnits9;
    originUnits9.SetUnits(LengthUnitName::UndefinedUnit, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit, TemperatureUnitName::CelsiusDegree);
    convertedUnits9.SetUnits(LengthUnitName::UndefinedUnit, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit, TemperatureUnitName::FahrenheitDegree);
    double convertdValue9 = UnitConverter::Convert(opUnitDim9.get(), 1.0, originUnits9, convertedUnits9);
    assert(abs(convertdValue9 - 33.8) <= 0.0001);

    // (m2/s2*℃)->(m2/s2*K)
    opUnitDim9->SetExponent(BaseUnit::Length, 2).SetExponent(BaseUnit::Time, -2).SetExponent(BaseUnit::Temperature, -1);
    originUnits9.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Second, CurrentUnitName::UndefinedUnit, TemperatureUnitName::CelsiusDegree);
    convertedUnits9.SetUnits(LengthUnitName::Meter, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::Second, CurrentUnitName::UndefinedUnit, TemperatureUnitName::KelvinScale);
    convertdValue9 = UnitConverter::Convert(opUnitDim9.get(), 10.0, originUnits9, convertedUnits9);
    assert(abs(convertdValue9 - 10.0) <= 0.0001);

    // 发光强度单位：毫坎德拉->坎德拉
    OwnerPtr<IUnitDimension> opUnitDim10 = IUnitDimension::Create();
    assert(opUnitDim10 != nullptr);
    opUnitDim10->SetExponent(BaseUnit::LuminousIntensity, 1);
    UnitDimensionNames originUnits10, convertedUnits10;
    originUnits10.SetUnits(LengthUnitName::UndefinedUnit, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit, TemperatureUnitName::CelsiusDegree, LuminousIntensityUnitName::Millicandela);
    convertedUnits10.SetUnits(LengthUnitName::UndefinedUnit, AngleUnitName::UndefinedUnit,
        MassUnitName::UndefinedUnit, TimeUnitName::UndefinedUnit, CurrentUnitName::UndefinedUnit, TemperatureUnitName::CelsiusDegree, LuminousIntensityUnitName::Candela);
    double convertdValue10 = UnitConverter::Convert(opUnitDim10.get(), 1000.0, originUnits10, convertedUnits10);
    assert(abs(convertdValue10 - 1.0) <= 0.0001);


    opUserTrans->Commit();
    return nullptr;
}

REGISTER_COMMAND(CmdUnitSample);
