﻿#include "GNodeWrapper.h"
#include "GbmpNew.h"
#include "ICanvas.h"
#include "IGraphicsNode.h"
#include "IGraphicsText.h"
#include "IGraphicsPlane.h"
#include "IGraphicsPoint.h"
#include "IGraphicsCurve3d.h"
#include "IGraphicsPicture.h"
#include "IGraphicsBRepBody.h"
#include "IGraphicsMeshBody.h"
#include "IGraphicsBRepFace.h"
#include "IGraphicsBRepEdge.h"
#include "IGraphicsRichText.h"
#include "IGraphicsGisLayer.h"
#include "IGraphicsZoomFree.h"
#include "IGraphicsNodeGroup.h"
#include "IGraphicsPolyCurve.h"
#include "IGraphicsPolyCurve3d.h"
#include "IGraphicsElementShape.h"
#include "IGraphicsNodeReference.h"
#include "IGraphicsNodeVisibilityFilter.h"
#include "IGraphicsElementShapeReference.h"
#include "ILinkElementModelShapeReference.h"
#include "IGraphicsOtherDocumentElementShapeReference.h"

#include "IGeometry.h"
#include "IArc2d.h"
#include "IArc3d.h"
#include "IBody.h"
#include "IConicalSurface.h"
#include "ICurve.h"
#include "ICurve2d.h"
#include "ICurve3d.h"
#include "ICylinder.h"
#include "IEdge.h"
#include "IEllipse2d.h"
#include "IEllipse3d.h"
#include "IFace.h"
#include "IGeometry.h"
#include "ILine2d.h"
#include "ILine3d.h"
#include "ILoop.h"
#include "IMesh.h"
#include "INurbsCurve2d.h"
#include "INurbsCurve3d.h"
#include "INurbsSurface.h"
#include "IPlane.h"
#include "IPlaneCurve3d.h"
#include "IPoint3d.h"
#include "IPolyCurve.h"
#include "IPolyCurve3d.h"
#include "IPolygon.h"
#include "ISphere.h"
#include "ISpiralLine2d.h"
#include "ISpiralLine3d.h"
#include "ISurface.h"
#include "ITorusSurface.h"
#include "ITrimmedCurve.h"
#include "ITrimmedSurface.h"
#include "IVertex.h"

#include "GNodeUtility.h"
#include "GeomWrapper.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

#pragma optimize("",off)
/// 本文件是界面实现的辅助类，不包含GDMP API的演示内容

using namespace gcmp;
using namespace Sample;

namespace Sample
{
std::map<FilterMode, std::wstring> IGraphicsNode_Wrapper::s_filterModeMap =
{
    { FilterMode::UNDEFINED, L"UNDEFINED" },
    { FilterMode::NEAREST, L"NEAREST" },
    { FilterMode::LINEAR, L"LINEAR" },
    { FilterMode::NEAREST_MIPMAP_NEAREST, L"NEAREST_MIPMAP_NEAREST" },
    { FilterMode::NEAREST_MIPMAP_LINEAR, L"NEAREST_MIPMAP_LINEAR" },
    { FilterMode::LINEAR_MIPMAP_NEAREST, L"LINEAR_MIPMAP_NEAREST" },
    { FilterMode::LINEAR_MIPMAP_LINEAR, L"LINEAR_MIPMAP_LINEAR" },
    { FilterMode::MAX_ANISOTROPIC_FILTER, L"MAX_ANISOTROPIC_FILTER" },
    { FilterMode::DISABLE_ANISOTROPIC_FILTER, L"DISABLE_ANISOTROPIC_FILTER" }
};

std::map<GraphicsRenderLayer, std::wstring> IGraphicsNode_Wrapper::s_gRenderLayerMap =
{
    { GraphicsRenderLayer::Unknown , L"Unknown" },
    { GraphicsRenderLayer::Model , L"Model" },
    { GraphicsRenderLayer::DrawingLine , L"DrawingLine" },
    { GraphicsRenderLayer::WorkPlane , L"WorkPlane" },
    { GraphicsRenderLayer::Annotation , L"Annotation" },
    { GraphicsRenderLayer::ReferenceObject , L"ReferenceObject" },
    { GraphicsRenderLayer::SketchObject , L"SketchObject" },
    { GraphicsRenderLayer::ProfileCurve , L"ProfileCurve" },
    { GraphicsRenderLayer::DimensionConstraint , L"DimensionConstraint" },
    { GraphicsRenderLayer::TransientObject , L"TransientObject" },
    { GraphicsRenderLayer::TransientObjectInTop , L"TransientObjectInTop" },
    { GraphicsRenderLayer::GISData , L"GISData" },
};

std::map<GraphicsNodeVisibility, std::wstring> IGraphicsNode_Wrapper::s_gNodeVisMap =
{
    { GraphicsNodeVisibility::Always, L"Always" },
    { GraphicsNodeVisibility::HighlightedExactly, L"HighlightedExactly" },
    { GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly, L"HighlightedExactly_Or_SelectedExactly" },
    { GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlighted, L"HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlighted" },
    { GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected, L"HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected" },
    { GraphicsNodeVisibility::Never, L"Never" },
    { GraphicsNodeVisibility::NeverHighlighted, L"NeverHighlighted" },
    { GraphicsNodeVisibility::NeverHighlighted_Or_Selected, L"NeverHighlighted_Or_Selected" },
};

std::map<PointDisplayShapeType, std::wstring> IGraphicsNode_Wrapper::s_pointShapeTypeMap =
{
    { PointDisplayShapeType::Circle, L"Circle" },
    { PointDisplayShapeType::Square, L"Square" },
    { PointDisplayShapeType::Cross, L"Cross" },
};

std::map<TextPrimitiveType, std::wstring> IGraphicsNode_Wrapper::s_textPrimitiveTypeMap =
{
    { TextPrimitiveType::Triangle, L"Triangle" },
    { TextPrimitiveType::Line, L"Line" },
};

std::map<AnchorAlignmentType, std::wstring> IGraphicsNode_Wrapper::s_textAnchorAlignmentTypeMap =
{
    { AnchorAlignmentType::Alignment, L"Alignment" },
    { AnchorAlignmentType::BottomCenter, L"BottomCenter" },
    { AnchorAlignmentType::Center, L"Center" },
    { AnchorAlignmentType::Full, L"Full" },
    { AnchorAlignmentType::HorizontalCenter, L"HorizontalCenter" },
    { AnchorAlignmentType::HorizontalLeft, L"HorizontalLeft" },
    { AnchorAlignmentType::HorizontalRight, L"HorizontalRight" },
    { AnchorAlignmentType::LeftBottom, L"LeftBottom" },
    { AnchorAlignmentType::LeftCenter, L"LeftCenter" },
    { AnchorAlignmentType::LeftTop, L"LeftTop" },
    { AnchorAlignmentType::Middle, L"Middle" },
    { AnchorAlignmentType::RightBottom, L"RightBottom" },
    { AnchorAlignmentType::RightCenter, L"RightCenter" },
    { AnchorAlignmentType::RightTop, L"RightTop" },
    { AnchorAlignmentType::TopCenter, L"TopCenter" },
};

std::map<gcmp::HorizontalAlignmentType, std::wstring> IGraphicsNode_Wrapper::s_HorizontalAlignmentTypeMap =
{
    { HorizontalAlignmentType::Center, L"Center" },
    { HorizontalAlignmentType::Left, L"Left" },
    { HorizontalAlignmentType::Right, L"Right" },
    { HorizontalAlignmentType::Scatter, L"Scatter" },
};
std::map<gcmp::VerticalAlignmentType, std::wstring> IGraphicsNode_Wrapper::s_VerticalAlignmentTypeMap =
{
    { VerticalAlignmentType::Bottom, L"Bottom" },
    { VerticalAlignmentType::Center, L"Center" },
    { VerticalAlignmentType::Top, L"Top" },
};
std::map<gcmp::HorizontalAnchorPointType, std::wstring> IGraphicsNode_Wrapper::s_HorizontalAnchorPointTypeMap =
{
    { HorizontalAnchorPointType::Center, L"Center" },
    { HorizontalAnchorPointType::Left, L"Left" },
    { HorizontalAnchorPointType::Right, L"Right" },
};
std::map<gcmp::VerticalAnchorPointType, std::wstring> IGraphicsNode_Wrapper::s_VerticalAnchorPointTypeMap =
{
    { VerticalAnchorPointType::Bottom, L"Bottom" },
    { VerticalAnchorPointType::Center, L"Center" },
    { VerticalAnchorPointType::Top, L"Top" },
};
std::map<gcmp::MeshEdgesVisibilityMode, std::wstring> IGraphicsNode_Wrapper::s_MeshEdgesVisibilityModeMap =
{
    { MeshEdgesVisibilityMode::AllEdgesVisible, L"AllEdgesVisible" },
    { MeshEdgesVisibilityMode::OpenMeshOnlyBoundariesInvisible, L"OpenMeshOnlyBoundariesInvisible" },
    { MeshEdgesVisibilityMode::OpenMeshOnlyBoundariesVisible, L"OpenMeshOnlyBoundariesVisible" },
    { MeshEdgesVisibilityMode::AllEdgesInvisible, L"AllEdgesInvisible" },
    { MeshEdgesVisibilityMode::Custom, L"Custom" },
};
std::map<gcmp::TextureRenderMode, std::wstring> IGraphicsNode_Wrapper::s_TextureRenderModeMap =
{
    { TextureRenderMode::Unknown, L"Unknown" },
    { TextureRenderMode::On, L"On" },
    { TextureRenderMode::Off, L"Off" }
};
std::map<gcmp::GraphicsNodeType, std::wstring> IGraphicsNode_Wrapper::s_GraphicsNodeTypeMap =
{
    { GraphicsNodeType::Arc2d, L"Arc2d" },
    { GraphicsNodeType::Arc3d, L"Arc3d" },
    { GraphicsNodeType::Body, L"Body" },
    { GraphicsNodeType::Curve2d, L"Curve2d" },
    { GraphicsNodeType::Curve3d, L"Curve3d" },
    { GraphicsNodeType::DocumentReference, L"DocumentReference" },
    { GraphicsNodeType::Edge, L"Edge" },
    { GraphicsNodeType::ElementShape, L"ElementShape" },
    { GraphicsNodeType::ElementShapeReference, L"ElementShapeReference" },
    { GraphicsNodeType::Ellipse2d, L"Ellipse2d" },
    { GraphicsNodeType::Ellipse3d, L"Ellipse3d" },
    { GraphicsNodeType::Face, L"Face" },
    { GraphicsNodeType::Fill, L"Fill" },
    { GraphicsNodeType::GISTile, L"GISTile" },
    { GraphicsNodeType::Group, L"Group" },
    { GraphicsNodeType::HiddenLine, L"HiddenLine" },
    { GraphicsNodeType::Image, L"Image" },
    { GraphicsNodeType::Line2d, L"Line2d" },
    { GraphicsNodeType::Line3d, L"Line3d" },
    { GraphicsNodeType::LinkedReference, L"LinkedReference" },
    { GraphicsNodeType::MeshBody, L"MeshBody" },
    { GraphicsNodeType::MeshBodyEdge, L"MeshBodyEdge" },
    { GraphicsNodeType::None, L"None" },
    { GraphicsNodeType::NurbsCurve2d, L"NurbsCurve2d" },
    { GraphicsNodeType::NurbsCurve3d, L"NurbsCurve3d" },
    { GraphicsNodeType::Picture, L"Picture" },
    { GraphicsNodeType::Plane3d, L"Plane3d" },
    { GraphicsNodeType::PlaneCurve, L"PlaneCurve" },
    { GraphicsNodeType::Point3d, L"Point3d" },
    { GraphicsNodeType::Pointset, L"Pointset" },
    { GraphicsNodeType::PolyCurve, L"PolyCurve" },
    { GraphicsNodeType::PolyCurve3d, L"PolyCurve3d" },
    { GraphicsNodeType::RichTex, L"RichTex" },
    { GraphicsNodeType::SpiralLine3d, L"SpiralLine3d" },
    { GraphicsNodeType::Text, L"Text" },
    { GraphicsNodeType::Zoomfree, L"Zoomfree" },
};

std::vector<std::wstring> IGraphicsNode_Wrapper::s_filterModeList = IGraphicsNode_Wrapper::mapToVector(s_filterModeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_gRenderLayerList = IGraphicsNode_Wrapper::mapToVector(s_gRenderLayerMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_gNodeVisList = IGraphicsNode_Wrapper::mapToVector(s_gNodeVisMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_pointShapeTypeList = IGraphicsNode_Wrapper::mapToVector(s_pointShapeTypeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_textPrimitiveTypeList = IGraphicsNode_Wrapper::mapToVector(s_textPrimitiveTypeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_textAnchorAlignmentTypeList = IGraphicsNode_Wrapper::mapToVector(s_textAnchorAlignmentTypeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_HorizontalAlignmentTypeList = IGraphicsNode_Wrapper::mapToVector(s_HorizontalAlignmentTypeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_VerticalAlignmentTypeList = IGraphicsNode_Wrapper::mapToVector(s_VerticalAlignmentTypeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_HorizontalAnchorPointTypeList = IGraphicsNode_Wrapper::mapToVector(s_HorizontalAnchorPointTypeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_VerticalAnchorPointTypeList = IGraphicsNode_Wrapper::mapToVector(s_VerticalAnchorPointTypeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_MeshEdgesVisibilityModeList = IGraphicsNode_Wrapper::mapToVector(s_MeshEdgesVisibilityModeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_TextureRenderModeList = IGraphicsNode_Wrapper::mapToVector(s_TextureRenderModeMap);
std::vector<std::wstring> IGraphicsNode_Wrapper::s_GraphicsNodeTypeList = IGraphicsNode_Wrapper::mapToVector(s_GraphicsNodeTypeMap);
}

namespace
{
    template<typename keyT>
    std::wstring enumFindKey(std::map<keyT, std::wstring>& map, keyT key)
    {
        auto pair = map.find(key);
        if (pair != map.end())
        {
            return pair->second;
        }
        return map.begin()->second;
    }

    template<typename keyT>
    keyT enumFindValue(std::map<keyT, std::wstring>& map, std::wstring value)
    {
        for (const auto& pair : map)
        {
            if (pair.second == value)
            {
                return pair.first;
            }
        }
        return map.begin()->first;
    }
}

IGraphicsNode_Wrapper* Sample::IGraphicsNode_Wrapper::GetRootParent()
{
    IGraphicsNode_Wrapper* pCurent = this;
    while (true)
    {
        IGraphicsNode_Wrapper* pParent = pCurent->GetParent();
        if (pParent == nullptr)
        {
            return pCurent;
        }
        pCurent = pParent;
    }
    return pCurent;
}

std::vector<OwnerPtr<IGraphicsNode_Wrapper>>& Sample::IGraphicsNode_Wrapper::GetChildren()
{
    return m_children;
}

void Sample::IGraphicsNode_Wrapper::SetGraphicsNodeIdPath(std::vector<gcmp::GraphicsNodeId> gNodeIdPath)
{
    m_graphicsNodeIdPath = gNodeIdPath;
}

gcmp::IGraphicsNodeReference& Sample::IGraphicsNode_Wrapper::GetGNodeReference()
{
    m_opGNodeReference = IGraphicsNodeReference::Create(m_elementId, m_graphicsNodeIdPath);
    return *m_opGNodeReference.get();
}

void Sample::IGraphicsNode_Wrapper::RunSetFieldFuncs()
{
    for (auto& pair : m_parametersArrayDict)
    {
        for (auto& itor : pair.second)
        {
            itor->RunSetFieldFunc();
        }
    }
    for (auto& itor : m_children)
    {
        itor->RunSetFieldFuncs();
    }
}

Sample::ParametersArrayDict& Sample::IGraphicsNode_Wrapper::GetParameters()
{
    return m_parametersArrayDict;
}

void Sample::IGraphicsNode_Wrapper::UpdateData()
{
    m_parametersArrayDict.clear();
    GNodeUtility::VisitGNode(GetGraphicsNode(), this);
}

void Sample::IGraphicsNode_Wrapper::VisitGNode()
{
    ParametersArray parameters;
    {
        GraphicsNodeType gNodeType = m_pGraphicsNode->GetType();
        std::wstring fieldValue = enumFindKey(s_GraphicsNodeTypeMap, gNodeType);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetType", fieldValue, s_GraphicsNodeTypeList);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        GraphicsRenderLayer renderLayer = m_pGraphicsNode->GetRenderLayer();
        std::wstring fieldValue = enumFindKey(s_gRenderLayerMap, renderLayer);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"RenderLayer", fieldValue, s_gRenderLayerList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            GraphicsRenderLayer renderLayer = enumFindValue(s_gRenderLayerMap, strValue);
            m_pGraphicsNode->SetRenderLayer(renderLayer);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        GraphicsNodeVisibility gNodeVis = m_pGraphicsNode->GetVisibility();
        std::wstring fieldValue = enumFindKey(s_gNodeVisMap, gNodeVis);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetVisibility", fieldValue, s_gNodeVisList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            GraphicsNodeVisibility gNodeVis = enumFindValue(s_gNodeVisMap, strValue);
            m_pGraphicsNode->SetVisibility(gNodeVis);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsVisible", m_pGraphicsNode->IsVisible());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsClippable", m_pGraphicsNode->IsClippable());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsNode::SetIsClippable, GetGraphicsNode()));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsCloneable", m_pGraphicsNode->IsCloneable());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsHiddenLineEnabled", m_pGraphicsNode->IsHiddenLineEnabled());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsNode::SetHiddenLineEnabled, GetGraphicsNode()));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsInTheBackground", m_pGraphicsNode->IsInTheBackground());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsNode::SetIsInTheBackground, GetGraphicsNode()));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsInTheForeground", m_pGraphicsNode->IsInTheForeground());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsNode::SetIsInTheForeground, GetGraphicsNode()));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSectioning", m_pGraphicsNode->IsSectioning());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSelectable", m_pGraphicsNode->IsSelectable());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsNode::SetIsSelectable, GetGraphicsNode()));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSnappable", m_pGraphicsNode->IsSnappable());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsNode::SetIsSnappable, GetGraphicsNode()));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetId", m_pGraphicsNode->GetId().AsInt());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        const IGraphicsNodeVisibilityFilter* pGNodeVisFilter = m_pGraphicsNode->GetVisibilityFilter();
        std::wstring filterName = L"nullptr";
        if (pGNodeVisFilter)
        {
            filterName = StringUtil::ToWString(typeid(*pGNodeVisFilter).name());
        }
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetVisibilityFilter", filterName);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        Box3d box = m_pGraphicsNode->GetBox();
        std::wstring boxTxt = box.IsEmpty() ? L"EMPTY" : box.GetSize().GetStringForDebug();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBox().GetSize()", boxTxt);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        ElementId gStyleId = m_pGraphicsNode->GetGraphicsStyleId();
        std::wstring gStyleIdTxt = gStyleId.IsValid() ? StringUtil::ToWString(gStyleId.AsInt64()) : L"Invalid";
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetGraphicsStyleId", gStyleIdTxt);
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring idStr)->void
        {
            bool flag;
            Int64 idValue = StringUtil::ToNumber<Int64>(idStr, &flag);
            if (flag)
            {
                m_pGraphicsNode->SetGraphicsStyleId(ElementId(idValue));
            }
            else
            {
                m_pGraphicsNode->SetGraphicsStyleId(ElementId::InvalidID);
            }
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        ElementId gMaterialId = m_pGraphicsNode->GetGraphicsMaterialId();
        std::wstring gMaterialIdTxt = gMaterialId.IsValid() ? StringUtil::ToWString(gMaterialId.AsInt64()) : L"Invalid";
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetGraphicsMaterialId", gMaterialIdTxt);
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsNode", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGBody(gcmp::IGraphicsBRepBody* pGBodyNode)
{
    ParametersArray parameters;
    m_pGraphicsBRepBody = pGBodyNode;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"FaceCount", pGBodyNode->GetFaceCount());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"EdgeCount", pGBodyNode->GetEdgeCount());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsShell", pGBodyNode->IsShell());

        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsDiscretizingAsAWhole", pGBodyNode->IsDiscretizingAsAWhole());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAngleDiscreteEpsilon", pGBodyNode->GetAngleDiscreteEpsilon());
        opField->SetSetDoubleFieldFunc(IFieldProxy::CreateSetDoubleFieldFunc(&IGraphicsBRepBody::SetAngleDiscreteEpsilon, m_pGraphicsBRepBody));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDistanceDiscreteEpsilon", pGBodyNode->GetDistanceDiscreteEpsilon());
        opField->SetSetDoubleFieldFunc(IFieldProxy::CreateSetDoubleFieldFunc(&IGraphicsBRepBody::SetDistanceDiscreteEpsilon, m_pGraphicsBRepBody));
        parameters.push_back(TransferOwnership(opField));
    }
    const IBody* pBody = m_pGraphicsBRepBody->GetGeometry();
    m_parametersArrayDict.emplace_back(L"IGraphicsBRepBody", TransferOwnership(parameters));
    IGeometry_Wrapper::Visit(pBody, m_parametersArrayDict);
}

void Sample::IGraphicsNode_Wrapper::VisitGCurve(gcmp::IGraphicsCurve3d* pGCurve)
{
    ParametersArray parameters;
    m_pGraphicsCurve3d = pGCurve;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsClosed", pGCurve->IsClosed());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"isProjectObject", pGCurve->isProjectObject());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsCurve3d::SetIsProjectObject, m_pGraphicsCurve3d));
        parameters.push_back(TransferOwnership(opField));
    }
    ICurve3d* pCrv = pGCurve->GetGmICurveFW();
    m_parametersArrayDict.emplace_back(L"IGraphicsCurve3d", TransferOwnership(parameters));
    IGeometry_Wrapper::Visit(pCrv, m_parametersArrayDict);
}

void Sample::IGraphicsNode_Wrapper::VisitGDocReference(gcmp::IGraphicsOtherDocumentElementShapeReference* pDocNode)
{
    ParametersArray parameters;
    m_pGraphicsOtherDocumentElementShapeReference = pDocNode;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetReferenceDocRuntimeId", pDocNode->GetReferenceDocRuntimeId().GetId());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsOtherDocumentElementShapeReference", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGEdge(gcmp::IGraphicsBRepEdge* pGEdgeNode)
{
    ParametersArray parameters;
    m_pGraphicsBRepEdge = pGEdgeNode;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsStraightLine", pGEdgeNode->IsStraightLine());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSeam", pGEdgeNode->IsSeam());
        parameters.push_back(TransferOwnership(opField));
    }
    const IEdge* pEdge = pGEdgeNode->GetGmEdge();
    const IBody* pBody = pEdge->GetBody();
    if (pBody->GetBrepType() != BrepType::Wire)
    {
        std::vector<const IGraphicsBRepFace*> pFaces = pGEdgeNode->GetFaces();
        std::wstring faceIds = L"";
        for (auto itor : pFaces)
        {
            faceIds += StringUtil::ToWString(itor->GetId().AsInt()) + L",";
        }
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFaces", faceIds);
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsBRepEdge", TransferOwnership(parameters));
    IGeometry_Wrapper::Visit(pEdge, m_parametersArrayDict);
}

void Sample::IGraphicsNode_Wrapper::VisitGFace(gcmp::IGraphicsBRepFace* pGFaceNode)
{
    ParametersArray parameters;
    m_pGraphicsBRepFace = pGFaceNode;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsPlanar", pGFaceNode->IsPlanar());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsCylindric", pGFaceNode->IsCylindric());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsShellFace", pGFaceNode->IsShellFace());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBackFaceVisible", pGFaceNode->IsBackFaceVisible());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsBRepFace::SetIsBackFaceVisible, pGFaceNode));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsTextureCoordinateNormalized", pGFaceNode->IsTextureCoordinateNormalized());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsBRepFace::SetIsTextureCoordinateNormalized, pGFaceNode));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        std::vector<const IGraphicsBRepEdge*> pEdges = pGFaceNode->GetEdges();
        std::wstring edgeIds = L"";
        for (auto itor : pEdges)
        {
            edgeIds += StringUtil::ToWString(itor->GetId().AsInt()) + L",";
        }
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFaces", edgeIds);
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsBRepFace", TransferOwnership(parameters));
    const IFace* pFace = pGFaceNode->GetGmFace();
    IGeometry_Wrapper::Visit(pFace, m_parametersArrayDict);
}

void Sample::IGraphicsNode_Wrapper::VisitGGroup(gcmp::IGraphicsNodeGroup* pGroup)
{
    ParametersArray parameters;
    m_pGraphicsNodeGroup = pGroup;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetChildrenCount", pGroup->GetChildrenCount());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsNodeGroup", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGZoomfree(IGraphicsZoomFree* pGZoomfree)
{
    ParametersArray parameters;
    m_pGraphicsZoomFree = pGZoomfree;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsFaceUpward_Count", pGZoomfree->IsFaceUpward(EnZoomFreeSubType::Count));
        IFieldProxy::SetBoolFieldFunc setBoolFunc = [&](bool flag)->void
        {
            m_pGraphicsZoomFree->SetIsFaceUpward(EnZoomFreeSubType::Count, flag);
        };
        opField->SetSetBoolFieldFunc(setBoolFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsFaceUpward_Curve2d", pGZoomfree->IsFaceUpward(EnZoomFreeSubType::Curve2d));
        IFieldProxy::SetBoolFieldFunc setBoolFunc = [&](bool flag)->void
        {
            m_pGraphicsZoomFree->SetIsFaceUpward(EnZoomFreeSubType::Curve2d, flag);
        };
        opField->SetSetBoolFieldFunc(setBoolFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsFaceUpward_Text", pGZoomfree->IsFaceUpward(EnZoomFreeSubType::Text));
        IFieldProxy::SetBoolFieldFunc setBoolFunc = [&](bool flag)->void
        {
            m_pGraphicsZoomFree->SetIsFaceUpward(EnZoomFreeSubType::Text, flag);
        };
        opField->SetSetBoolFieldFunc(setBoolFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsZoomable", pGZoomfree->IsZoomable());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsZoomFree::SetZoomable, pGZoomfree));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetChildrenCount", pGZoomfree->GetChildrenCount());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsZoomFree", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGISTile(gcmp::IGraphicsGisLayer* pGISTile)
{
    ParametersArray parameters;
    m_pGraphicsGisLayer = pGISTile;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetLayerName", pGISTile->GetLayerName());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsGisLayer", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGInstance(gcmp::IGraphicsElementShapeReference* pInstanceNode)
{
    ParametersArray parameters;
    m_pGraphicsElementShapeReference = pInstanceNode;
    VisitGNode();
    {
        Int64 idValue = pInstanceNode->GetReferencedElementId().GetId();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetReferencedElementId", StringUtil::ToWString(idValue));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        Matrix4d matrix = pInstanceNode->GetTransformMatrix();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTransformMatrix", matrix.GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsElementShapeReference", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGLinkReference(gcmp::ILinkElementModelShapeReference* pLinkElementNode)
{
    ParametersArray parameters;
    m_pLinkElementModelShapeReference = pLinkElementNode;
    VisitGNode();
    m_parametersArrayDict.emplace_back(L"ILinkElementModelShapeReference", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGMeshBody(gcmp::IGraphicsMeshBody* pGMeshBodyNode)
{
    ParametersArray parameters;
    m_pGraphicsMeshBody = pGMeshBodyNode;
    VisitGNode();
    const gcmp::IMesh* pMesh = m_pGraphicsMeshBody->GetMesh();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pMesh, L"断言`pMesh`失败", L"GDMPLab", L"2024-03-30");
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBackFaceVisible", m_pGraphicsMeshBody->IsBackFaceVisible());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsMeshBody::SetIsBackFaceVisible, pGMeshBodyNode));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        MeshEdgesVisibilityMode meshEdgesVisMode = m_pGraphicsMeshBody->GetEdgesVisibilityMode();
        std::wstring meshEdgesVisModeStr = enumFindKey(s_MeshEdgesVisibilityModeMap, meshEdgesVisMode);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEdgesVisibilityMode", meshEdgesVisModeStr, s_MeshEdgesVisibilityModeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            MeshEdgesVisibilityMode meshVisType = enumFindValue(s_MeshEdgesVisibilityModeMap, strValue);
            m_pGraphicsMeshBody->SetEdgesVisibilityMode(meshVisType);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsMeshBody", TransferOwnership(parameters));
    IGeometry_Wrapper::Visit(pMesh, m_parametersArrayDict);
}

void Sample::IGraphicsNode_Wrapper::VisitGPicture(gcmp::IGraphicsPicture* pPictureNode)
{
    ParametersArray parameters;
    m_pGraphicsPicture = pPictureNode;
    VisitGNode();
    {
        PictureId id = pPictureNode->GetPictureId();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPictureId", static_cast<int>(id));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPicturePath", pPictureNode->GetPicturePath());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        FilterMode filterMode = pPictureNode->GetFilterMode();
        std::wstring filterModeStr = enumFindKey(s_filterModeMap, filterMode);

        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFilterMode", filterModeStr, s_filterModeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            FilterMode filterMode = enumFindValue(s_filterModeMap, strValue);
            m_pGraphicsPicture->SetFilterMode(filterMode);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsPicture", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGPlane(gcmp::IGraphicsPlane* pGPlaneNode)
{
    ParametersArray parameters;
    m_pGraphicsPlane = pGPlaneNode;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStartU", pGPlaneNode->GetStartU());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEndU", pGPlaneNode->GetEndU());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStartV", pGPlaneNode->GetStartV());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEndV", pGPlaneNode->GetEndV());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsPlane", TransferOwnership(parameters));
    IGeometry_Wrapper::Visit(pGPlaneNode->GetGmIPlane(), m_parametersArrayDict);
}

void Sample::IGraphicsNode_Wrapper::VisitGPoint(gcmp::IGraphicsPoint* pGPointNode)
{
    ParametersArray parameters;
    m_pGraphicsPoint = pGPointNode;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsDepthNormalMode", pGPointNode->IsDepthNormalMode());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsPoint::SetDepthNormalMode, pGPointNode));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPointSize", pGPointNode->GetPointSize());
        opField->SetSetDoubleFieldFunc(IFieldProxy::CreateSetDoubleFieldFunc(&IGraphicsPoint::SetPointSize, pGPointNode));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        PointDisplayShapeType pointShapeType = pGPointNode->GetDisplayShape();
        std::wstring displayType = enumFindKey(s_pointShapeTypeMap, pointShapeType);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDisplayShape", displayType, s_pointShapeTypeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            PointDisplayShapeType pointShapeType = enumFindValue(s_pointShapeTypeMap, strValue);
            m_pGraphicsPoint->SetDisplayShape(pointShapeType);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsPoint", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGPolyCurve(gcmp::IGraphicsPolyCurve* pPolyCurve)
{
    ParametersArray parameters;
    m_pGraphicsPolyCurve = pPolyCurve;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCurveCount", pPolyCurve->GetCurveCount());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsEnableLinetypeGeneration", pPolyCurve->IsEnableLinetypeGeneration());
        IFieldProxy::SetBoolFieldFunc setBoolFunc = [&](bool flag)->void
        {
            if (flag)
            {
                m_pGraphicsPolyCurve->EnableLinetypeGeneration();
            }
            else
            {
                m_pGraphicsPolyCurve->DisableLinetypeGeneration();
            }
        };

        opField->SetSetBoolFieldFunc(setBoolFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetLineWidth", pPolyCurve->GetLineWidth());
        opField->SetSetDoubleFieldFuncBool(IFieldProxy::CreateSetDoubleFieldFuncBool(&IGraphicsPolyCurve::SetLineWidth, pPolyCurve));
        parameters.push_back(TransferOwnership(opField));
    }
    for (int i = 0; i < pPolyCurve->GetCurveCount(); i++)
    {
        OwnerPtr<IFieldProxy> opField0 = NEW_AS_OWNER_PTR(IFieldProxy, L"GetSegmentLineWidth_Start_" + StringUtil::ToWString(i), pPolyCurve->GetSegmentLineWidth(i).first);
        OwnerPtr<IFieldProxy> opField1 = NEW_AS_OWNER_PTR(IFieldProxy, L"GetSegmentLineWidth_End_" + StringUtil::ToWString(i), pPolyCurve->GetSegmentLineWidth(i).second);
        IFieldProxy::SetDoubleFieldFunc setDoubleFunc0 = [index = i, &pPlCrv = m_pGraphicsPolyCurve](double v)->void
        {
            double secondWidth = pPlCrv->GetSegmentLineWidth(index).second;
            pPlCrv->SetSegmentLineWidth(index, v, secondWidth);
        };
        IFieldProxy::SetDoubleFieldFunc setDoubleFunc1 = [index = i, &pPlCrv = m_pGraphicsPolyCurve](double v)->void
        {
            double firstWidth = pPlCrv->GetSegmentLineWidth(index).first;
            pPlCrv->SetSegmentLineWidth(index, firstWidth, v);
        };

        opField0->SetSetDoubleFieldFunc(setDoubleFunc0);
        opField1->SetSetDoubleFieldFunc(setDoubleFunc1);
        parameters.push_back(TransferOwnership(opField0));
        parameters.push_back(TransferOwnership(opField1));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsPolyCurve", TransferOwnership(parameters));
    IGeometry_Wrapper::Visit(m_pGraphicsPolyCurve->GetGeometry(), m_parametersArrayDict);
}

void Sample::IGraphicsNode_Wrapper::VisitGPolyCurve3d(gcmp::IGraphicsPolyCurve3d* pPolyCurve3d)
{
    ParametersArray parameters;
    m_pGraphicsPolyCurve3d = pPolyCurve3d;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCurveCount", pPolyCurve3d->GetCurveCount());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetLineWidth", pPolyCurve3d->GetLineWidth());
        opField->SetSetDoubleFieldFuncBool(IFieldProxy::CreateSetDoubleFieldFuncBool(&IGraphicsPolyCurve3d::SetLineWidth, pPolyCurve3d));
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsPolyCurve3d", TransferOwnership(parameters));
    IGeometry_Wrapper::Visit(m_pGraphicsPolyCurve3d->GetGeometry(), m_parametersArrayDict);
}

void Sample::IGraphicsNode_Wrapper::VisitGRep(gcmp::IGraphicsElementShape* pGRepNode)
{
    ParametersArray parameters;
    m_pGraphicsElementShape = pGRepNode;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetChildrenCount", pGRepNode->GetChildrenCount());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsHighlightable", pGRepNode->IsHighlightable());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsModelViewSpecific", pGRepNode->IsModelViewSpecific());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsElementTransient", pGRepNode->IsElementTransient());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsEnableAnnotationClip", pGRepNode->IsEnableAnnotationClip());
        IFieldProxy::SetBoolFieldFunc setBoolFunc = [&](bool flag)->void
        {
            m_pGraphicsElementShape->EnableAnnotationClip();
        };

        opField->SetSetBoolFieldFunc(setBoolFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        Byte priorityOffset = pGRepNode->GetPriorityOffset();
        int intOffset = static_cast<int>(priorityOffset);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPriorityOffset", intOffset);
        IFieldProxy::SetIntFieldFunc setIntFunc = [&](int intValue)->void
        {
            Byte priority = static_cast<Byte>(intValue);
            m_pGraphicsElementShape->SetPriorityOffset(priority);
        };

        opField->SetSetIntFieldFunc(setIntFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsEnableBatchLoad", pGRepNode->IsEnableBatchLoad());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsElementShape", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitGText(gcmp::IGraphicsText* pGTextNode)
{
    ParametersArray parameters;
    m_pGraphicsText = pGTextNode;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCharSize ", pGTextNode->GetCharSize());
        IFieldProxy::SetDoubleFieldFunc setFunc = [&](double v)->void
        {
            m_pGraphicsText->SetCharSize((float)v);
        };
        opField->SetSetDoubleFieldFunc(setFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetContent", pGTextNode->GetContent());
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring txt)->void
        {
            m_pGraphicsText->SetContent(txt);
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetMaxWidth ", pGTextNode->GetMaxWidth());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWidthFactor ", pGTextNode->GetWidthFactor());
        IFieldProxy::SetDoubleFieldFunc setFunc = [&](double v)->void
        {
            m_pGraphicsText->SetWidthFactor((float)v);
        };
        opField->SetSetDoubleFieldFunc(setFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsMirroredInX ", pGTextNode->IsMirroredInX());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsText::SetIsMirroredInX, m_pGraphicsText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsMirroredInY ", pGTextNode->IsMirroredInY());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsText::SetIsMirroredInY, m_pGraphicsText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsUseProjectionLineColor ", pGTextNode->IsUseProjectionLineColor());
        opField->SetSetBoolFieldFunc(IFieldProxy::CreateSetBoolFieldFunc(&IGraphicsText::SetUseProjectionLineColor, m_pGraphicsText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFontFilePath ", pGTextNode->GetFontFilePath());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        TextPrimitiveType textType = pGTextNode->GetTextPrimitiveType();
        std::wstring textTypeStr = enumFindKey(s_textPrimitiveTypeMap, textType);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTextPrimitiveType ", textTypeStr, s_textPrimitiveTypeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            TextPrimitiveType textPType = enumFindValue(s_textPrimitiveTypeMap, strValue);
            m_pGraphicsText->SetTextPrimitiveType(textPType);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        AnchorAlignmentType textType = pGTextNode->GetAnchorAlignment();
        std::wstring textTypeStr = enumFindKey(s_textAnchorAlignmentTypeMap, textType);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAnchorAlignment", textTypeStr, s_textAnchorAlignmentTypeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            AnchorAlignmentType textPType = enumFindValue(s_textAnchorAlignmentTypeMap, strValue);
            m_pGraphicsText->SetAnchorAlignment(textPType);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPosition ", pGTextNode->GetPosition());
        opField->SetSetVector3dFieldFuncBool(IFieldProxy::CreateSetVector3dFieldFuncBool(&IGraphicsText::SetPosition, m_pGraphicsText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetNormal ", pGTextNode->GetNormal());
        opField->SetSetVector3dFieldFunc(IFieldProxy::CreateSetVector3dFieldFunc(&IGraphicsText::SetNormal, m_pGraphicsText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetMaxWidth ", pGTextNode->GetMaxWidth());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsText", TransferOwnership(parameters));
}

void Sample::IGraphicsNode_Wrapper::VisitRichText(gcmp::IGraphicsRichText* pRichTextNode)
{
    m_pGraphicsRichText = pRichTextNode;
    ParametersArray parameters;
    VisitGNode();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStringForDebug", pRichTextNode->GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAcadStyleString", pRichTextNode->GetAcadStyleString());
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring txt)->void
        {
            m_pGraphicsRichText->SetAcadStyleString(txt);
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCharHeight", pRichTextNode->GetCharHeight());
        IFieldProxy::SetDoubleFieldFunc setFunc = [&](double v)->void
        {
            std::wstring errStr;
            m_pGraphicsRichText->SetCharHeight(v, &errStr);
        };
        opField->SetSetDoubleFieldFunc(setFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDirection", pRichTextNode->GetDirection());
        IFieldProxy::SetVector3dFieldFunc setDirectionFunc = [&](const Vector3d& dir)->void
        {
            m_pGraphicsRichText->SetDirection(dir);
        };
        opField->SetSetVector3dFieldFunc(setDirectionFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetNormal", pRichTextNode->GetNormal());
        IFieldProxy::SetVector3dFieldFunc setNormalFunc = [&](const Vector3d& normal)->void
        {
            m_pGraphicsRichText->SetNormal(normal);
        };
        opField->SetSetVector3dFieldFunc(setNormalFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBold", pRichTextNode->IsBold());
        IFieldProxy::SetBoolFieldFunc setBoldFunc = [&](bool flag)->void
        {
            if (flag)
            {
                m_pGraphicsRichText->Bold();
            }
            else
            {
                m_pGraphicsRichText->UnBold();
            }
        };
        opField->SetSetBoolFieldFunc(setBoldFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsItalic", pRichTextNode->IsItalic());
        IFieldProxy::SetBoolFieldFunc setItalicFunc = [&](bool flag)->void
        {
            if (flag)
            {
                m_pGraphicsRichText->Italic();
            }
            else
            {
                m_pGraphicsRichText->UnItalic();
            }
        };
        opField->SetSetBoolFieldFunc(setItalicFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsUnderLine", pRichTextNode->IsUnderLine());
        IFieldProxy::SetBoolFieldFunc setUnderLineFunc = [&](bool flag)->void
        {
            if (flag)
            {
                m_pGraphicsRichText->UnderLine();
            }
            else
            {
                m_pGraphicsRichText->DeUnderLine();
            }
        };
        opField->SetSetBoolFieldFunc(setUnderLineFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTextAreaRealHeight ", pRichTextNode->GetTextAreaRealHeight());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTextAreaRealWidth ", pRichTextNode->GetTextAreaRealWidth());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetLineSpaceScale", pRichTextNode->GetLineSpaceScale());
        opField->SetSetDoubleFieldFuncBool(IFieldProxy::CreateSetDoubleFieldFuncBool(&IGraphicsRichText::SetLineSpaceScale, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsAutoWrap ", pRichTextNode->IsAutoWrap());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsRichText::SetIsAutoWrap, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCharacterSpacing", pRichTextNode->GetCharacterSpacing());
        opField->SetSetDoubleFieldFuncBool(IFieldProxy::CreateSetDoubleFieldFuncBool(&IGraphicsRichText::SetCharacterSpacing, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWidthFactor", pRichTextNode->GetWidthFactor());
        opField->SetSetDoubleFieldFuncBool(IFieldProxy::CreateSetDoubleFieldFuncBool(&IGraphicsRichText::SetWidthFactor, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTextAreaWidth", pRichTextNode->GetTextAreaWidth());
        opField->SetSetDoubleFieldFuncBool(IFieldProxy::CreateSetDoubleFieldFuncBool(&IGraphicsRichText::SetTextAreaWidth, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTextAreaHeight ", pRichTextNode->GetTextAreaHeight());
        opField->SetSetDoubleFieldFuncBool(IFieldProxy::CreateSetDoubleFieldFuncBool(&IGraphicsRichText::SetTextAreaHeight, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        ElementId backgroundFillId = pRichTextNode->GetBackgroundFillNodeGraphicsStyleId();
        std::wstring backgroundFillIdTxt = backgroundFillId.IsValid() ? StringUtil::ToWString(backgroundFillId.AsInt64()) : L"Invalid";
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBackgroundFillNodeGraphicsStyleId", backgroundFillIdTxt);
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring idStr)->void
        {
            bool flag;
            Int64 idValue = StringUtil::ToNumber<Int64>(idStr, &flag);
            if (flag)
            {
                m_pGraphicsRichText->SetBackgroundFillNodeGraphicsStyleId(ElementId(idValue));
            }
            else
            {
                m_pGraphicsRichText->SetBackgroundFillNodeGraphicsStyleId(ElementId::InvalidID);
            }
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsEnableThemeDefaultColor ", pRichTextNode->IsEnableThemeDefaultColor());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsRichText::SetIsEnableThemeDefaultColor, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBigFontPriority ", pRichTextNode->IsBigFontPriority());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsRichText::SetIsBigFontPriority, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsNeedDefaultBlank ", pRichTextNode->IsNeedDefaultBlank());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsRichText::SetIsNeedDefaultBlank, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsTransformAdjustReadability ", pRichTextNode->IsTransformAdjustReadability());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsRichText::SetIsTransformAdjustReadability, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsUpdateLocalTextStyle ", pRichTextNode->IsUpdateLocalTextStyle());
        opField->SetSetBoolFieldFuncBool(IFieldProxy::CreateSetBoolFieldFuncBool(&IGraphicsRichText::SetIsUpdateLocalTextStyle, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFont ", pRichTextNode->GetFont());
        opField->SetSetTextConstRefFieldFuncBool(IFieldProxy::CreateSetTextConstRefFieldFuncBool(&IGraphicsRichText::SetFont, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBigFont ", pRichTextNode->GetBigFont());
        opField->SetSetTextConstRefFieldFuncBool(IFieldProxy::CreateSetTextConstRefFieldFuncBool(&IGraphicsRichText::SetBigFont, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTextStyleName", pRichTextNode->GetTextStyleName());
        opField->SetSetTextConstRefFieldFuncBool(IFieldProxy::CreateSetTextConstRefFieldFuncBool(&IGraphicsRichText::SetTextStyleName, m_pGraphicsRichText));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEnglishFont", pRichTextNode->GetEnglishAndChineseFont().first);
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring txt)->void
        {
            std::wstring chineseFont = m_pGraphicsRichText->GetEnglishAndChineseFont().second;
            m_pGraphicsRichText->SetEnglishAndChineseFont(txt, chineseFont);
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GeChineseFont", pRichTextNode->GetEnglishAndChineseFont().second);
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring txt)->void
        {
            std::wstring englishFont = m_pGraphicsRichText->GetEnglishAndChineseFont().first;
            m_pGraphicsRichText->SetEnglishAndChineseFont(englishFont, txt);
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        HorizontalAlignmentType  textType = pRichTextNode->GetHorizontalAlignmentType();
        std::wstring textTypeStr = enumFindKey(s_HorizontalAlignmentTypeMap, textType);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHorizontalAlignmentType", textTypeStr, s_HorizontalAlignmentTypeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            HorizontalAlignmentType textPType = enumFindValue(s_HorizontalAlignmentTypeMap, strValue);
            m_pGraphicsRichText->SetHorizontalAlignmentType(textPType);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        VerticalAlignmentType  textType = pRichTextNode->GetVerticalAlignmentType();
        std::wstring textTypeStr = enumFindKey(s_VerticalAlignmentTypeMap, textType);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetVerticalAlignmentType", textTypeStr, s_VerticalAlignmentTypeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            VerticalAlignmentType textPType = enumFindValue(s_VerticalAlignmentTypeMap, strValue);
            m_pGraphicsRichText->SetVerticalAlignmentType(textPType);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        HorizontalAnchorPointType textType = pRichTextNode->GetHorizontalAnchorPointType();
        std::wstring textTypeStr = enumFindKey(s_HorizontalAnchorPointTypeMap, textType);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHorizontalAnchorPointType", textTypeStr, s_HorizontalAnchorPointTypeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            HorizontalAnchorPointType textPType = enumFindValue(s_HorizontalAnchorPointTypeMap, strValue);
            m_pGraphicsRichText->SetHorizontalAnchorPointType(textPType);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        VerticalAnchorPointType textType = pRichTextNode->GetVerticalAnchorPointType();
        std::wstring textTypeStr = enumFindKey(s_VerticalAnchorPointTypeMap, textType);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetVerticalAnchorPointType", textTypeStr, s_VerticalAnchorPointTypeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            VerticalAnchorPointType textPType = enumFindValue(s_VerticalAnchorPointTypeMap, strValue);
            m_pGraphicsRichText->SetVerticalAnchorPointType(textPType);
        };

        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        Vector3d Txtpos = pRichTextNode->GetAnchorPointPosition();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAnchorPointPosition", Txtpos);
        IFieldProxy::SetVector3dFieldFuncBool setPosFunc = [&](Vector3d pos)->bool
        {
            return m_pGraphicsRichText->SetAnchorPointPosition(pos);
        };

        opField->SetSetVector3dFieldFuncBool(setPosFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTextLayoutParameters",
            L"X:" + StringUtil::ToWString(pRichTextNode->GetTextLayoutParameters().first) +
            L", Y:" + StringUtil::ToWString(pRichTextNode->GetTextLayoutParameters().second));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        TextureRenderMode textureRenderMode = pRichTextNode->GetTextureRenderMode();
        std::wstring textureRenderModeStr = enumFindKey(s_TextureRenderModeMap, textureRenderMode);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTextureRenderMode", textureRenderModeStr, s_TextureRenderModeList);
        IFieldProxy::SetTextOrEnumFieldFunc setEnumFunc = [&](std::wstring strValue)->void
        {
            TextureRenderMode mode = enumFindValue(s_TextureRenderModeMap, strValue);
            m_pGraphicsRichText->SetTextureRenderMode(mode);
        };
        opField->SetSetEnumFieldFunc(setEnumFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetUsedFont", pRichTextNode->GetUsedFont());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetUsedBigFont", pRichTextNode->GetUsedBigFont());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IGraphicsRichText", TransferOwnership(parameters));
}

IGraphicsNode_Wrapper::IGraphicsNode_Wrapper(gcmp::ElementId id, gcmp::IGraphicsNode* pGraphicsNode, bool isReadOnly, IGraphicsNode_Wrapper* pParent)
    : ObjectWrapper(StringUtil::ToWString(typeid(*pGraphicsNode).name())),
    m_elementId(id), m_pGraphicsNode(pGraphicsNode), m_isReadOnly(isReadOnly), m_pParent(pParent)
{
    StringUtil::ReplaceAll(m_objectName, L"class gcmp::", L"");
}



