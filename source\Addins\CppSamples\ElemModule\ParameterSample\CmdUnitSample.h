﻿#pragma once

#include "CommandBase.h"

using namespace gcmp;

namespace Sample
{
    class CmdUnitSample : public CommandBase
    {
    public:
        CmdUnitSample();

    public:
        virtual OwnerPtr<IAction> ExecuteCommand(const gcmp::CommandParameters& cmdParams) override;
        virtual bool IsEnabled() const override { return true; }
        virtual bool IsVisible() const override { return true; }
        virtual bool ShouldClearSelectionBeforeExecution() const override { return true; }
    };
}
