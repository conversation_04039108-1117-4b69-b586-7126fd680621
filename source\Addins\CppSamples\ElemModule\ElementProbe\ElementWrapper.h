﻿#pragma once

#include "ElementId.h"

#include <map>
#include "ObjectBrowser/IFieldProxy.h"
#include "ObjectBrowser/ObjectWrapper.h"

using namespace gcmp;

namespace gcmp
{
    class IDocument;
    class IElement;
    class IInstance;
    class IParameter;
    class IFamilyType;
    class IInstanceType;
    class IExternalData;
    class IPositionGeometry;
    class IElementParameters;
    class IPositionAssociatedPlane;
    class IElementParametersCustomizer;
    class IElementParameterBindings;
}

namespace gfam
{
    class IFamilyManager;
    class IFamilyParameterDefinition;
}

namespace Sample
{
    // 一个图元的混色设置数据
    class ElementWrapper : public ObjectWrapper
    {
    public:
        ElementWrapper(ElementId elemId);

        virtual ~ElementWrapper(){}

        void CreateParam_IElement();
        void CreateParam_IElementBasicInformation();
        void CreateParam_IElementStatus();
        void CreateParam_ILayerComponent();
        void CreateParam_IElementTags();
        void CreateParam_IElementAttributes();
        void CreateParam_IExternalDataComponent();
        void CreateParam_IExternalData(const OwnerPtr<gcmp::IExternalData>& opExData);
        void CreateParam_IExternalObject();
        void CreateParam_IElementParameters();
        void CreateParam_IElementParameterBindings(const gcmp::IElementParameterBindings* pParamBindings);
        void CreateParam_IElementParametersCustomizer(const gcmp::IElementParametersCustomizer* pParamCustomizer);
        void CreateParam_IParameter(OwnerPtr<gcmp::IParameter>& opParam);
        void CreateParam_IElementParameterGroupOverride();
        void CreateParam_IElementRegenerationComponent();
        void CreateParam_IElementDeletionComponent();
        void CreateParam_IElementTransformationComponent();
        void CreateParam_IElementCopyPasteComponent();
        void CreateParam_IElementCopyStrategyComponent();
        void CreateParam_IGroupComponent();
        void CreateParam_IElementPosition();
        void CreateParam_IPositionGeometry();
        void CreateParam_IPositionCurve2d(const IPositionGeometry* pPosGeom);
        void CreateParam_IPositionPoint2d(const IPositionGeometry* pPosGeom);
        void CreateParam_IPositionPointOnCurve2d(const IPositionGeometry* pPosGeom);
        void CreateParam_IPositionPointRelativeHostPoint2d(const IPositionGeometry* pPosGeom);
        void CreateParam_IPositionPolyCurve2d(const IPositionGeometry* pPosGeom);
        void CreateParam_IPositionAssociatedPlane();
        void CreateParam_IPositionAssociatedCoordinate(const IPositionAssociatedPlane* pPosPlane);
        void CreateParam_IPositionSingleAssociatedPlane(const IPositionAssociatedPlane* pPosPlane);
        void CreateParam_IPositionSingleAssociatedPlaneWithTwoOffsets(const IPositionAssociatedPlane* pPosPlane);
        void CreateParam_IPositionTwoAssociatedPlanes(const IPositionAssociatedPlane* pPosPlane);
        void CreateParam_IPositionTwoAssociatedPlanesWithFourOffsets(const IPositionAssociatedPlane* pPosPlane);
        void CreateParam_IElementPositionPoints();
        void CreateParam_IElementPositionReportComponent();
        void CreateParam_IElementModelShape();
        void CreateParam_IElementViewSpecificShapeComponent();
        void CreateParam_IGraphicsNodeStyleAndMaterialOverride();
        void CreateParam_IElementModelingOperations();
        void CreateParam_IGeometryRelationshipComponent();
        void CreateParam_IElementConstraintBehavior();
        void CreateParam_ICommandBehavior();

        void CreateParam_IOpeningRelationshipBehavior();
        void CreateParam_IOpenedGraphicsElementShapeComponent();
        void CreateParam_IOpeningGraphicsElementShapeComponent();
        void CreateParam_ICutRelationshipBehavior();
        void CreateParam_ICutterGraphicsElementShapeComponent();
        void CreateParam_ICutteeGraphicsElementShapeComponent();
        void CreateParam_IJoinRelationshipBehavior();
        void CreateParam_IGeometryRelationship(int index);

        void CreateParam_IInstance();
        void CreateParam_IInstance_IInstanceType();
        void CreateParam_IInstance_IFamily();
        void CreateParam_IInstance_IFamilyManager();
        void CreateParam_IInstance_IFamilyConfigElement();
        void CreateParam_IInstance_IElementParameters(const gcmp::IElementParameters* pElemParams);

        void CreateParam_IInstance_IElementParameters_IParameter(OwnerPtr<gcmp::IParameter> opParam);
        void CreateParam_IInstance_IFamilyParameterDefinition_IParameter(IDocument* pFamDoc, const gfam::IFamilyParameterDefinition* pFamParamDef);
        void CreateParam_IInstance_IFamily_IFamilyType(const IFamilyType* pFamType);
        void CreateParam_IInstance_IFamily_InstanceType(const gcmp::IInstanceType* pInstType);
        void CreateParam_IInstance_IFamilyManager_IFamilyType(const IFamilyType* pFamType, const gfam::IFamilyManager* pFamMgr);

        virtual ParametersArrayDict& GetParameters() override;
        virtual void RunSetFieldFuncs() override;
        ElementId GetElementId() { return m_elementId; }
    private:
        gcmp::IElement* GetElement();
        gcmp::IInstance* GetInstance();
    private:
        ElementId m_elementId;
        ParametersArrayDict m_parametersArrayDict;
    };
}

