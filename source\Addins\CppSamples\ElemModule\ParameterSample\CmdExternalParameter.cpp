﻿#include "CmdExternalParameter.h"
#include "IDocument.h"
#include "IDocumentManager.h"
#include "UniIdentity.h"
#include "ExternalParameterType.h"
#include "IDocumentInitializer.h"
#include "IExternalParameterDefinition.h"
#include "IExternalParameterDefinitionBinding.h"
#include "IExternalParameterDefinitionManager.h"
#include "IExternalParameterDefinitionFile.h"
#include "GcmpBuiltInCategoryUniIdentities.h"
#include "IUserTransaction.h"
#include "IParameterValueList.h"
#include "GcmpCommandState.h"
#include "CommandRegister.h"
#include "ElementId.h"
#include "IParameter.h"
#include "ILine3d.h"
#include "IModelLine.h"
#include "LogManager.h"
#include "FilePath.h"
#include "GbmpFileSystem.h"
#include "IParameterDefinition.h"
#include "UiDocumentViewUtils.h"
#include "IElementParameters.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;
using namespace std;

#define ID_CMD_EXTERNAL_PARAMETER L"CmdExternalParameter"

CmdExternalParameter::CmdExternalParameter(void)
    : CommandBase(ID_CMD_EXTERNAL_PARAMETER)
{
}

CmdExternalParameter::~CmdExternalParameter(void)
{
}

gcmp::OwnerPtr<IAction> CmdExternalParameter::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    // 示例1：创建项目参数并绑定
    if (!CreateProjectParameter())
    {
        return nullptr;
    }

    // 示例2：创建和管理共享参数
    if (!CreateSharedParameter())
    {
        return nullptr;
    }

    // 示例3：加载和保存外部参数
    if (!LoadAndSaveExternalParameters())
    {
        return nullptr;
    }

    // 示例4：用共享参数文件的参数绑定
    if (!LoadAndBindExternalParameters())
    {
        return nullptr;
    }

    // 示例5：绑定外部参数到模型元素的各种操作示例
    if (!ExternalParameterSample())
    {
        return nullptr;
    }

    return nullptr;
}

bool CmdExternalParameter::CreateProjectParameter()
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pDoc, L"pDoc为空", L"GDMPLab", L"2024-12-30");

    OwnerPtr<IUserTransaction> transaction = IUserTransaction::Create(pDoc, L"Create External Parameter");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(transaction, L"transaction为空", L"GDMPLab", L"2024-12-30");

    std::vector<UniIdentity> categories({ BuiltInCategoryUniIdentities::BICU_MODEL_LINE, BuiltInCategoryUniIdentities::BICU_MODEL_VIEW });

    IExternalParameterDefinitionManager* pExternalParameterDefinitionManager = IExternalParameterDefinitionManager::GetFW(pDoc);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pExternalParameterDefinitionManager, L"pExternalParameterDefinitionManager为空", L"GDMPLab", L"2024-12-30");


    UniIdentity paramUid = UniIdentity::Create(GuidUtils::FromString(L"{D2092E61-01E5-4858-9CED-4B970B2635EC}"), L"项目共享参数测试");

    if (nullptr != pExternalParameterDefinitionManager->GetExternalParameterDefinitionByUid(paramUid))
    {
        return true;
    }

    OwnerPtr<IExternalParameterDefinitionBinding> pBinding = IExternalParameterDefinitionBinding::Create(false, categories);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pBinding, L"pBinding为空", L"GDMPLab", L"2024-12-30");
    IExternalParameterDefinition* pExternalParameterDefinition = pExternalParameterDefinitionManager->CreateExternalParameterDefinition(paramUid, L"项目参数",
        L"共享参数测试", L"添加项目共享参数", ExternalParameterType::Integer, true, true);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pExternalParameterDefinition, L"pExternalParameterDefinition为空", L"GDMPLab", L"2024-12-30");

    pExternalParameterDefinitionManager->Bind(pExternalParameterDefinition->GetParameterDefinitionId(), pBinding.get());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pExternalParameterDefinition->GetBinding(), L"绑定失败", L"GDMPLab", L"2024-12-30");

    std::vector<IElement*> pElems = pDoc->GetElementsByCategory(BuiltInCategoryUniIdentities::BICU_MODEL_VIEW);
    for (auto pElem : pElems)
    {
        IElementParameters* pElemParameters = pElem->GetElementParameters();
        OwnerPtr<IParameter> opParam = pElemParameters->GetParameterByUid(paramUid);
        opParam->SetValueAsInt(888888);
        std::wstring errorMsg;
        pElemParameters->SetParameter(opParam.get(), &errorMsg);
    }

    transaction->Commit();

    return true;
}

bool CmdExternalParameter::CreateSharedParameter()
{
    // 获取唯一单例对象
    IExternalParameterDefinitionFile* definitionFile = IExternalParameterDefinitionFile::Instance();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(definitionFile, L"definitionFile为空", L"GDMPLab", L"2024-12-30");

    // 创建参数定义组
    IExternalParameterDefinitionGroup* group1 = definitionFile->AddGroup(L"Group1");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(group1, L"group1为空", L"GDMPLab", L"2024-12-30");
    
    IExternalParameterDefinitionGroup* group2 = definitionFile->AddGroup(L"Group2");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(group2, L"group2为空", L"GDMPLab", L"2024-12-30");

    // 获取所属的文件
    const IExternalParameterDefinitionFile* getFile1 = group1->GetFile();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(getFile1, L"getFile1为空", L"GDMPLab", L"2024-12-30");
    
    const IExternalParameterDefinitionFile* getFile2 = group2->GetFile();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(getFile2, L"getFile2为空", L"GDMPLab", L"2024-12-30");

    // 获取所有参数定义组
    std::vector<const IExternalParameterDefinitionGroup*> allGroupsVec = definitionFile->GetGroups();


    const UniIdentity uid1 = UniIdentity::Create(GuidUtils::FromString(L"{310D4B5B-213A-4561-B757-62A830437433}"), L"共享参数文件测试1");
    const UniIdentity uid2 = UniIdentity::Create(GuidUtils::FromString(L"{C57EEBFA-7049-4277-A902-AC9E298755FE}"), L"共享参数文件测试2");

    const std::vector<std::pair<std::wstring, std::wstring>> valueList;
    // 创建参数定义项
    IExternalParameterDefinitionItem* item1 = group1->AddItem(uid1, L"Param1", L"External Parameter Item1", ExternalParameterType::Length, true, false, valueList);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(item1, L"item1为空", L"GDMPLab", L"2024-12-30");
    
    IExternalParameterDefinitionItem* item2 = group1->AddItem(uid2, L"Param2", L"External Parameter Item2", ExternalParameterType::Angle, true, false, valueList);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(item2, L"item2为空", L"GDMPLab", L"2024-12-30");

    // 获取该组所有外部参数定义项
    std::vector<const IExternalParameterDefinitionItem*> getItemsVec = group1->GetItems();
    
    // 根据参数名称获取参数定义项
    const IExternalParameterDefinitionItem* getItem1 = definitionFile->GetItem(L"Param1");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(getItem1, L"getItem1为空", L"GDMPLab", L"2024-12-30");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(getItem1->GetDescription() == L"External Parameter Item1", L"参数描述不匹配", L"GDMPLab", L"2024-12-30");
    
    const IExternalParameterDefinitionItem* getItem2 = group1->GetItem(L"Param2");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(getItem2, L"getItem2为空", L"GDMPLab", L"2024-12-30");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(getItem2->GetParameterType() == ExternalParameterType::Angle, L"参数类型不匹配", L"GDMPLab", L"2024-12-30");

    // 根据uid删除参数定义项
    group1->RemoveItem(uid2);
    getItemsVec = group1->GetItems();
    
    // 根据组名称获取定义组
    const IExternalParameterDefinitionGroup* getGroup = definitionFile->GetGroup(L"Group1");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(getGroup, L"getGroup为空", L"GDMPLab", L"2024-12-30");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(getGroup->GetName() == L"Group1", L"组名不匹配", L"GDMPLab", L"2024-12-30");

    // 根据组名称移除定义组
    definitionFile->RemoveGroup(L"Group2");
    allGroupsVec = definitionFile->GetGroups();
    
    definitionFile->Save(FilePath::Combine(FileSystem::GetExeDirPath(), L"/data/TestExternalParameters.xml"));

    return true;
}

bool CmdExternalParameter::LoadAndSaveExternalParameters()
{
    // 获取唯一单例对象
    IExternalParameterDefinitionFile* definitionFile = IExternalParameterDefinitionFile::Instance();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(definitionFile, L"definitionFile为空", L"GDMPLab", L"2024-12-30");

    // 尝试从文件加载外部参数定义
    // 注意：在实际应用中，需要确保文件路径存在
    bool loadSucceed = definitionFile->Load(FilePath::Combine(FileSystem::GetExeDirPath(), L"/data/TestExternalParameters.xml").c_str());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(loadSucceed, L"加载外部参数定义失败", L"GDMPLab", L"2024-12-30");

    // 创建参数定义组
    IExternalParameterDefinitionGroup* newGroup1 = definitionFile->AddGroup(L"newGroup1");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(newGroup1, L"newGroup1为空", L"GDMPLab", L"2024-12-30");
    
    IExternalParameterDefinitionGroup* newGroup2 = definitionFile->AddGroup(L"newGroup2");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(newGroup2, L"newGroup2为空", L"GDMPLab", L"2024-12-30");

    const UniIdentity uid1 = UniIdentity::Create(GuidUtils::FromString(L"{A1A1AD78-DBFC-43B1-B753-32F436944216}"), L"共享参数文件测试3");
    const UniIdentity uid2 = UniIdentity::Create(GuidUtils::FromString(L"{9D48CD27-E81D-4DF2-B85C-44524323E016}"), L"共享参数文件测试4");

    const std::vector<std::pair<std::wstring, std::wstring>> valueList;
    // 创建参数定义项
    IExternalParameterDefinitionItem* newItem1 = newGroup1->AddItem(uid1, L"Param3", L"External Parameter", ExternalParameterType::Length, true, false, valueList);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(newItem1, L"newItem1为空", L"GDMPLab", L"2024-12-30");
    
    IExternalParameterDefinitionItem* newItem2 = newGroup2->AddItem(uid2, L"Param4", L"External Parameter", ExternalParameterType::Angle, true, false, valueList);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(newItem2, L"newItem2为空", L"GDMPLab", L"2024-12-30");

    // 保存当前内容到外部文件
    bool saveSucceed = definitionFile->Save(FilePath::Combine(FileSystem::GetExeDirPath(), L"TestExternalParameter.xml").c_str());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(saveSucceed, L"保存外部参数文件失败", L"GDMPLab", L"2024-12-30");

    return true;
}

bool CmdExternalParameter::LoadAndBindExternalParameters()
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pDoc, L"pDoc为空", L"GDMPLab", L"2024-12-30");

    // 获取唯一单例对象
    IExternalParameterDefinitionFile* definitionFile = IExternalParameterDefinitionFile::Instance();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(definitionFile, L"definitionFile为空", L"GDMPLab", L"2024-12-30");

    // 尝试从文件加载外部参数定义
    // 注意：在实际应用中，需要确保文件路径存在
    bool loadSucceed = definitionFile->Load(FilePath::Combine(FileSystem::GetExeDirPath(), L"/data/TestExternalParameters.xml").c_str());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(loadSucceed, L"加载外部参数定义失败", L"GDMPLab", L"2024-12-30");

    OwnerPtr<IUserTransaction> transaction = IUserTransaction::Create(pDoc, L"Create External Parameter");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(transaction, L"transaction为空", L"GDMPLab", L"2024-12-30");

    std::vector<UniIdentity> categories({ BuiltInCategoryUniIdentities::BICU_MODEL_LINE, BuiltInCategoryUniIdentities::BICU_MODEL_VIEW, BuiltInCategoryUniIdentities::BICU_GROUP });
    OwnerPtr<IExternalParameterDefinitionBinding> pBinding = IExternalParameterDefinitionBinding::Create(false, categories);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pBinding, L"pBinding为空", L"GDMPLab", L"2024-12-30");

    IExternalParameterDefinitionManager* pExternalParameterDefinitionManager = IExternalParameterDefinitionManager::GetFW(pDoc);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pExternalParameterDefinitionManager, L"pExternalParameterDefinitionManager为空", L"GDMPLab", L"2024-12-30");

    for (const IExternalParameterDefinitionGroup* pExternalParamDefGroup : definitionFile->GetGroups())
    {
        for (const IExternalParameterDefinitionItem* pExternalParamDefItem : pExternalParamDefGroup->GetItems())
        {
            IExternalParameterDefinition* pExternalParameterDefinition = pExternalParameterDefinitionManager->CreateExternalParameterDefinition(pExternalParamDefItem);
            DBG_WARN_AND_RETURN_FALSE_UNLESS(pExternalParameterDefinition, L"pExternalParameterDefinition为空", L"GDMPLab", L"2024-12-30");

            pExternalParameterDefinitionManager->Bind(pExternalParameterDefinition->GetParameterDefinitionId(), pBinding.get());
            DBG_WARN_AND_RETURN_FALSE_UNLESS(pExternalParameterDefinition->GetBinding(), L"绑定失败", L"GDMPLab", L"2024-12-30");
        }
    }
    transaction->Commit();

    return true;
}

bool CmdExternalParameter::ExternalParameterSample()
{
    // 创建文档
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();

    // 创建事务
    DBG_WARN_AND_RETURN_FALSE_UNLESS(!IUserTransaction::IsInUserTransaction(pDoc), L"已在事务中", L"GDMPLab", L"2024-12-30");
    OwnerPtr<IUserTransaction> opUserTransaction = IUserTransaction::Create(pDoc, GBMP_TR(L"创建外部参数定义"));
    DBG_WARN_AND_RETURN_FALSE_UNLESS(opUserTransaction, L"opUserTransaction为空", L"GDMPLab", L"2024-12-30");

    // 创建一条模型线，获取ElemId
    Vector3d startPt(1000, 0, 0);
    Vector3d endPt(0, 1000, 0);
    OwnerPtr<ILine3d> opLine3d = ILine3d::Create(startPt, endPt);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(opLine3d, L"opLine3d为空", L"GDMPLab", L"2024-12-30");
    IModelLine* pModelLine = IModelLine::Create(pDoc, TransferOwnershipCast<ICurve3d>(opLine3d->Clone()));
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pModelLine, L"pModelLine为空", L"GDMPLab", L"2024-12-30");
    IElement* pModelLineElem = pModelLine->GetOwnerElement();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pModelLineElem, L"pModelLineElem为空", L"GDMPLab", L"2024-12-30");
    ElementId elementId1 = pModelLineElem->GetElementId();

    // 获取文档中唯一的外部参数定义管理类对象，如果不存在会新建一个
    IExternalParameterDefinitionManager* pExParamManager = IExternalParameterDefinitionManager::GetFW(pDoc);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pExParamManager, L"pExParamManager为空", L"GDMPLab", L"2024-12-30");

    // 获取参数id
    const UniIdentity uid = UniIdentity::Create();

    // 创建一个自定义外部参数定义
    const std::wstring name = L"Test No.1";
    const std::wstring groupName = L"Test Group No.1";
    const std::wstring description = L"This is a test to bind to instance.";
    IExternalParameterDefinition* pExParamDefinition = pExParamManager->CreateExternalParameterDefinition(uid, name, groupName, description, ExternalParameterType::Integer, true, true, nullptr);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pExParamDefinition, L"pExParamDefinition为空", L"GDMPLab", L"2024-12-30");

    // 设置绑定的类别列表--模型线
    std::vector<UniIdentity> categories;
    categories.push_back(BuiltInCategoryUniIdentities::BICU_MODEL_LINE);

    // 将外部参数定义绑定到实例上
    OwnerPtr<IExternalParameterDefinitionBinding> opBinding = IExternalParameterDefinitionBinding::Create(false, categories);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(opBinding, L"opBinding为空", L"GDMPLab", L"2024-12-30");
    int paramDefinitionId = pExParamDefinition->GetParameterDefinitionId();
    pExParamManager->Bind(paramDefinitionId, opBinding.get());

    // 获取绑定的参数类型
    ExternalParameterType bindedType = pExParamDefinition->GetParameterType();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(bindedType == ExternalParameterType::Integer, L"参数类型不匹配", L"GDMPLab", L"2024-12-30");

    // 获取参数定义对象
    const gcmp::IParameterDefinition* pBindedDefinition = pExParamDefinition->GetParameterDefinition();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pBindedDefinition, L"pBindedDefinition为空", L"GDMPLab", L"2024-12-30");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pBindedDefinition->GetDescription() == description, L"参数描述不匹配", L"GDMPLab", L"2024-12-30");

    // 验证绑定的外部参数定义是否为用户可编辑
    bool modifiable = pExParamDefinition->IsUserModifiable();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(modifiable, L"参数不可编辑", L"GDMPLab", L"2024-12-30");

    // 验证绑定的外部参数定义是否为用户可见
    bool visiable = pExParamDefinition->IsUserVisible();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(visiable, L"参数不可见", L"GDMPLab", L"2024-12-30");

    // 根据Id获取外部参数定义
    const IExternalParameterDefinition* pGetExParamDefById = pExParamManager->GetExternalParameterDefinitionById(paramDefinitionId);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGetExParamDefById, L"pGetExParamDefById为空", L"GDMPLab", L"2024-12-30");
    const IParameterDefinition* pGetParamDefById = pGetExParamDefById->GetParameterDefinition();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGetParamDefById, L"pGetParamDefById为空", L"GDMPLab", L"2024-12-30");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGetParamDefById->GetName() == name, L"参数名称不匹配", L"GDMPLab", L"2024-12-30");

    // 根据uid获取外部参数定义
    const IExternalParameterDefinition* pGetExDefByUid = pExParamManager->GetExternalParameterDefinitionByUid(uid);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGetExDefByUid, L"pGetExDefByUid为空", L"GDMPLab", L"2024-12-30");
    const IParameterDefinition* pGetParamDefByUid = pGetExDefByUid->GetParameterDefinition();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGetParamDefByUid->GetGroupName() == groupName, L"参数组名不匹配", L"GDMPLab", L"2024-12-30");

    // 获取所有的外部参数定义
    std::vector<const IExternalParameterDefinition*> pGetDefinitionVec = pExParamManager->GetAllExternalParameterDefinitions();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGetDefinitionVec.capacity() == 1, L"外部参数定义数量不匹配", L"GDMPLab", L"2024-12-30");

    // 判断图元是否绑定了指定的参数定义
    bool hasBinded = pExParamManager->HasParameterById(pModelLineElem, paramDefinitionId);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(hasBinded, L"图元未绑定指定参数定义", L"GDMPLab", L"2024-12-30");

    // 获取图元绑定的指定参数定义Id上的参数
    OwnerPtr<IParameter> opGetParam = pExParamManager->GetParameterById(pModelLineElem, paramDefinitionId);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(opGetParam, L"opGetParam为空", L"GDMPLab", L"2024-12-30");
    const IParameterDefinition* pGetParamDef = opGetParam->GetParameterDefinition();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGetParamDef, L"pGetParamDef为空", L"GDMPLab", L"2024-12-30");
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGetParamDef->GetUid() == uid, L"参数UID不匹配", L"GDMPLab", L"2024-12-30");

    // 解绑参数定义
    pExParamManager->Unbind(pExParamDefinition->GetParameterDefinitionId());
    hasBinded = pExParamManager->HasParameterById(pModelLineElem, paramDefinitionId);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(!hasBinded, L"解绑失败", L"GDMPLab", L"2024-12-30");

    opUserTransaction->Commit();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(IDocumentManager::Get()->CloseDocument(pDoc->GetRuntimeId()), L"关闭文档失败", L"GDMPLab", L"2024-12-30");

    return true;
}

REGISTER_COMMAND(CmdExternalParameter);