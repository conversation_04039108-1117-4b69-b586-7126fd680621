﻿#include "ElementWrapper.h"
#include "ILayer.h"
#include "ICurve2d.h"
#include "IFamily.h"
#include "IElement.h"
#include "IDocument.h"
#include "IInstance.h"
#include "IGeometry.h"
#include "NdbObject.h"
#include "ILayerData.h"
#include "IParameter.h"
#include "IPolyCurve.h"
#include "UniIdentity.h"
#include "ICalculator.h"
#include "IFamilyType.h"
#include "IElementTags.h"
#include "IPasteHelper.h"
#include "UndoRedoUtils.h"
#include "IExternalData.h"
#include "IInstanceType.h"
#include "IFamilyManager.h"
#include "NdbClassSchema.h"
#include "ICloneBehavior.h"
#include "IElementStatus.h"
#include "IExternalDatas.h"
#include "IFamilyManager.h"
#include "ILayerComponent.h"
#include "IGroupComponent.h"
#include "IGenericElement.h"
#include "IExternalObject.h"
#include "IUserTransaction.h"
#include "IElementPosition.h"
#include "IPositionCurve2d.h"
#include "ICommandBehavior.h"
#include "IPositionPoint2d.h"
#include "IDocumentManager.h"
#include "DocumentPathUtils.h"
#include "NdbPropertySchema.h"
#include "IPositionGeometry.h"
#include "IElementAttributes.h"
#include "IElementParameters.h"
#include "IElementModelShape.h"
#include "IParameterValidator.h"
#include "ISerializationBehavior.h"
#include "IModelingOperation.h"
#include "UiDocumentViewUtils.h"
#include "UndoRedoEntityBrief.h"
#include "IParameterValueList.h"
#include "FamilyParameterType.h"
#include "IParameterDefinition.h"
#include "IPositionPolyCurve2d.h"
#include "IFamilyConfigElement.h"
#include "ICalculatorCollection.h"
#include "IParameterGroupSorter.h"
#include "IGraphicsElementShape.h"
#include "IGeometryRelationship.h"
#include "IExternalDataComponent.h"
#include "IElementPositionPoints.h"
#include "IModelViewDataAccessor.h"
#include "IParameterValueStorage.h"
#include "IPositionPointOnCurve2d.h"
#include "IElementBasicInformation.h"
#include "ICutRelationshipBehavior.h"
#include "UserTransactionGroupUtils.h"
#include "IElementDeletionComponent.h"
#include "IJoinRelationshipBehavior.h"
#include "IElementCopyPasteComponent.h"
#include "IElementModelingOperations.h"
#include "IElementConstraintBehavior.h"
#include "IFamilyParameterDefinition.h"
#include "IPositionTwoAssociatedPlanes.h"
#include "IOpeningRelationshipBehavior.h"
#include "IElementParametersCustomizer.h"
#include "IElementRegenerationComponent.h"
#include "IPositionAssociatedCoordinate.h"
#include "IGeometryRelationshipComponent.h"
#include "IPositionSingleAssociatedPlane.h"
#include "IElementParameterGroupOverride.h"
#include "IElementTransformationComponent.h"
#include "IElementPositionReportComponent.h"
#include "IPositionPointRelativeHostPoint2d.h"
#include "IElementViewSpecificShapeComponent.h"
#include "IOpenedGraphicsElementShapeComponent.h"
#include "ICutterGraphicsElementShapeComponent.h"
#include "ICutteeGraphicsElementShapeComponent.h"
#include "IGraphicsNodeStyleAndMaterialOverride.h"
#include "IOpeningGraphicsElementShapeComponent.h"
#include "IPositionTwoAssociatedPlanesWithFourOffsets.h"
#include "IPositionSingleAssociatedPlaneWithTwoOffsets.h"
#include "IElementParameterBindings.h"
#include "IParameterBinding.h"

#include "ObjectBrowser/IFieldProxy.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace Sample;
using namespace gcmp;

namespace
{
    void AddRegenDataId(const RegenDataId& rdId, ParametersArray& parameters, std::wstring functionName)
    {
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, functionName+L"ObjectId", StringUtil::ToWString(rdId.ObjectId));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, functionName + L"functionName_DataId", StringUtil::ToWString(rdId.DataId));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, functionName + L"functionName_ExtendedId", StringUtil::ToWString(rdId.ExtendedId));
            parameters.push_back(TransferOwnership(opField));
        }
    }

    void AddDBObjectInfo(const IDbObject* pDbObject, ParametersArray& parameters)
    {
        {
            const ICloneBehavior* pCloneBehavior = pDbObject->GetCloneBehavior();
            std::wstring cloneBehaviorStr = pCloneBehavior == nullptr ? L"nullptr" : StringUtil::ToWString(typeid(*pCloneBehavior).name());
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCloneBehavior", cloneBehaviorStr);
            parameters.push_back(TransferOwnership(opField));
        }
        {
            const ISerializationBehavior* pSerialBehavior = pDbObject->GetSerializationBehavior();
            std::wstring serialBehaviorStr = pSerialBehavior == nullptr ? L"nullptr" : StringUtil::ToWString(typeid(*pSerialBehavior).name());
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetSerializationBehavior", serialBehaviorStr);
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetClassGuid", GuidUtils::ToString(pDbObject->GetClassGuid()));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetClassId", pDbObject->GetClassId().AsWString());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"ClassType", StringUtil::ToWString(pDbObject->ClassType()));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"typeid", StringUtil::ToWString(typeid(*pDbObject).name()));
            parameters.push_back(TransferOwnership(opField));
        }
    }

    std::wstring ToWString(Color& color)
    {
        return std::to_wstring(color.R) + L"," + std::to_wstring(color.G) + L"," + std::to_wstring(color.B);
    }

    void AddParameterValueAsString(const OwnerPtr<IParameter>& opParam, ParametersArray& parameters, const gfam::IFamilyParameterDefinition* pFamParamDef = nullptr, bool addStorageType = false)
    {
        std::wstring storageType;
        std::wstring rslt;

        const IParameterValueStorage* pParamValueStorage = opParam->GetParameterValueStorage();
        ParameterStorageType paramStorageType = pParamValueStorage->GetParameterStorageType();
        if (paramStorageType == ParameterStorageType::Bool) { storageType = L"Bool"; rslt = StringUtil::ToWString(opParam->GetValueAsBool()); }
        else if (paramStorageType == ParameterStorageType::BuildingStorey) { storageType = L"BuildingStorey";  rslt = opParam->GetValueAsString(); }
        else if (paramStorageType == ParameterStorageType::Color) { storageType = L"Color"; rslt = ToWString(opParam->GetValueAsColor()); }
        else if (paramStorageType == ParameterStorageType::Coordinates) { storageType = L"Coordinates"; }
        else if (paramStorageType == ParameterStorageType::Count) { storageType = L"Count"; rslt = StringUtil::ToWString(opParam->GetValueAsInt()); }
        else if (paramStorageType == ParameterStorageType::Custom) {
            storageType = L"Custom";
            const IParameterCustomData* pParamCustomData = opParam->GetValueAsCustomData();
            rslt = StringUtil::ToWString(typeid(pParamCustomData).name());
        }
        else if (paramStorageType == ParameterStorageType::Double) { storageType = L"Double"; rslt = StringUtil::ToWString(opParam->GetValueAsDouble()); }
        else if (paramStorageType == ParameterStorageType::ElementId) { storageType = L"ElementId"; rslt = StringUtil::ToWString(opParam->GetValueAsElementId().AsInt64()); }
        else if (paramStorageType == ParameterStorageType::Geometry) {
            storageType = L"Geometry";
            const IGeometry* pGeom = opParam->GetValueAsGeometry();
            rslt = StringUtil::ToWString(typeid(pGeom).name());
        }
        else if (paramStorageType == ParameterStorageType::Guid) { storageType = L"Guid"; rslt = GuidUtils::ToString(opParam->GetValueAsGuid()); }
        else if (paramStorageType == ParameterStorageType::Int) { storageType = L"Int"; rslt = StringUtil::ToWString(opParam->GetValueAsInt()); }
        else if (paramStorageType == ParameterStorageType::Invalid) { storageType = L"Invalid"; }
        else if (paramStorageType == ParameterStorageType::None) { storageType = L"None"; }
        else if (paramStorageType == ParameterStorageType::StandardCodeReference) { storageType = L"StandardCodeReference"; }
        else if (paramStorageType == ParameterStorageType::String) { storageType = L"String"; rslt = opParam->GetValueAsString(); }
        
        if(addStorageType)
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStorageType", storageType);
            parameters.push_back(TransferOwnership(opField));
        }
        if(addStorageType)
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetValue###", rslt);
            parameters.push_back(TransferOwnership(opField));
        }
        else
        {
            std::wstring paramName = opParam->GetParameterDefinitionUid().GetDebugString();
            if (pFamParamDef != nullptr)
            {
                paramName = pFamParamDef->GetName();
            }
            else
            {
                paramName = opParam->GetName();
            }
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, paramName, rslt);
            parameters.push_back(TransferOwnership(opField));
        }
    }

    void CreateParameter(const OwnerPtr<IParameter>& opParam, ParametersArray& parameters, const IElement* pElem)
    {
        std::wstring idStr = L"_" + StringUtil::ToWString(opParam->GetParameterDefinitionId());
        const IParameterDefinition* pParamDef = opParam->GetParameterDefinition();
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetName", opParam->GetName());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetGroupName", pParamDef->GetGroupName());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetUnitTypeId", pParamDef->GetDescription());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetParameterTypeId", pParamDef->GetParameterTypeId().GetGuidAndDebugString());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetUid", pParamDef->GetUid().GetGuidAndDebugString());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetUnitTypeId", pParamDef->GetUnitTypeId().GetGuidAndDebugString());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            AddParameterValueAsString(opParam, parameters, nullptr, true);
        }
        {
            const IParameterValueList* pValueList = pParamDef->GetValueList();
            if (pValueList)
            {
                std::vector<std::wstring> txts = pValueList->GetDisplayStrings(pElem->GetDocument());
                if (txts.size() > 0)
                {
                    std::wstring result;
                    for (const auto& wstr : txts) {
                        result += wstr;
                    }
                    OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetValueList", result);
                    parameters.push_back(TransferOwnership(opField));
                }
            }
        }
        {
            const IParameterValidator* pParamValidator = opParam->GetValidator();
            if (pParamValidator)
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetValidator", StringUtil::ToWString(typeid(*pParamValidator).name()));
                parameters.push_back(TransferOwnership(opField));
            }
            else
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetValidator", L"nullptr");
                parameters.push_back(TransferOwnership(opField));
            }
        }
        {
            std::wstring rslt;
            switch (opParam->GetParameterProcessType())
            {
            case ParameterProcessType::Bound: rslt = L"Bound"; break;
            case ParameterProcessType::Count: rslt = L"Count"; break;
            case ParameterProcessType::Custom: rslt = L"Custom"; break;
            case ParameterProcessType::Default: rslt = L"Default"; break;
            case ParameterProcessType::GeneralInput: rslt = L"GeneralInput"; break;
            case ParameterProcessType::GeneralOutput: rslt = L"GeneralOutput"; break;
            case ParameterProcessType::Invalid: rslt = L"Invalid"; break;
            default:
                break;
            }
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetParameterProcessType", rslt);
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBindable", opParam->IsBindable());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBoundParameter", opParam->IsBoundParameter());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsDriveParameter", opParam->IsDriveParameter());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsGeometricParameter", opParam->IsGeometricParameter());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsInputParameter", opParam->IsInputParameter());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsModifiable", opParam->IsModifiable());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsOutputParameter", opParam->IsOutputParameter());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsTypeParameter", opParam->IsTypeParameter());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsUserVisible", opParam->IsUserVisible());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsValidParameter", IParameter::IsValidParameter(opParam));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsValidDocumentParameter", opParam->IsValidDocumentParameter());
            parameters.push_back(TransferOwnership(opField));
        }
    }

    void CreateCalculators(OwnerPtr<ICalculatorCollection>& opCalculators, ParametersArrayDict& parametersArrayDict)
    {
        for (int i = 0; i < opCalculators->GetCalculatorCount(); i++)
        {
            ParametersArray parameters;
            std::wstring suffix = L"_" + StringUtil::ToWString(i);
            const ICalculator* pCalculator = opCalculators->GetCalcualtorByIndex(i);
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"typeid" + suffix, StringUtil::ToWString(typeid(*pCalculator).name()));
                parameters.push_back(TransferOwnership(opField));
            }
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCalculatorName" + suffix, pCalculator->GetCalculatorName());
                parameters.push_back(TransferOwnership(opField));
            }
            {
                AddRegenDataId(pCalculator->GetRegenDataId(), parameters, L"GetCalculatorName" + suffix);
                std::vector<RegenDataId> rdIds;
                pCalculator->ReportInputDataIds(rdIds);
                for (int i = 0; i < rdIds.size(); i++)
                {
                    AddRegenDataId(rdIds[i], parameters, L"ReportInputDataIds_" + suffix + L"_" + StringUtil::ToWString(i));
                }
            }
            parametersArrayDict.emplace_back(L"ICalculator" + suffix, TransferOwnership(parameters));
        }
    }

    void CreateParam_NdbClassSchema(const NdbClassSchema* pClassSchema, ParametersArrayDict& parametersArrayDict)
    {
        DBG_WARN_AND_RETURN_VOID_UNLESS(pClassSchema, L"pClassSchema为空",L"GDMPLab",L"2024-12-30");
        ParametersArray parametersClassSchema;
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetName", pClassSchema->GetName());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAssemblyId", GuidUtils::ToString(pClassSchema->GetAssemblyId()));
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAssemblyName", pClassSchema->GetAssemblyName());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetIndex", pClassSchema->GetIndex());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetClassSchemaSignature", pClassSchema->GetClassSchemaSignature());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            const std::vector<NdbClassSchema*> pChildrenSchema = pClassSchema->GetChildrenClassSchemas();
            std::wstring childrenTxt;
            for (auto it : pChildrenSchema)
            {
                childrenTxt += it->GetName() + L";";
            }
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetChildrenClassSchemas", childrenTxt);
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetParentClassId", pClassSchema->GetParentClassId().AsWString());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            NdbClassSchema* pParentSchema = pClassSchema->GetParentClassSchema();
            std::wstring pParentSchemaName = pParentSchema == nullptr ? L"nullptr" : pParentSchema->GetName();
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetParentClassSchema", pParentSchemaName);
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetVersion", pClassSchema->GetVersion());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBottomClass", pClassSchema->IsBottomClass());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsIntrinsic", pClassSchema->IsIntrinsic());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsTopClass", pClassSchema->IsTopClass());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        {
            std::wstring errorMsg;
            bool isValid = pClassSchema->IsValid(&errorMsg);
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsValid", isValid);
                parametersClassSchema.push_back(TransferOwnership(opField));
            }
            if (isValid)
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsValid_error", errorMsg);
                parametersClassSchema.push_back(TransferOwnership(opField));
            }
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"ShouldArchive", pClassSchema->ShouldArchive());
            parametersClassSchema.push_back(TransferOwnership(opField));
        }
        parametersArrayDict.emplace_back(L"NdbClassSchema", TransferOwnership(parametersClassSchema));
        ParametersArray parametersProperty;
        {
            const std::vector<NdbPropertySchema> propertySchemas = pClassSchema->GetPropertySchemas();
            int index = 0;
            for (const NdbPropertySchema& propSchema : propertySchemas)
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPropertySchemas_" + StringUtil::ToWString(index), propSchema.ToWString());
                parametersClassSchema.push_back(TransferOwnership(opField));
                index++;
            }
        }
        parametersArrayDict.emplace_back(L"NdbPropertySchema", TransferOwnership(parametersProperty));
        return;
    }
}

void ElementWrapper::CreateParam_IElement()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetImplementationUid_Guid", pElem->GetImplementationUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementId", pElem->GetElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHostElementId", pElem->GetHostElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetGroupId", pElem->GetGroupId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsInGroupType", pElem->IsInGroupType());
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pElem, parameters);
    m_parametersArrayDict.emplace_back(L"IElement", TransferOwnership(parameters));
    return;
}

void ElementWrapper::CreateParam_IElementBasicInformation()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    const IElementBasicInformation* pBasicInfo = pElem->GetBasicInformation();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pBasicInfo, L"pClassSchema为空",L"GDMPLab",L"2024-12-30");
    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCategoryUid", pBasicInfo->GetCategoryUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetClassficationUid", pBasicInfo->GetClassficationUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDefaultTypeName", pBasicInfo->GetDefaultTypeName());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementId", pBasicInfo->GetElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementUid", pBasicInfo->GetElementUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetImplementationUid", pBasicInfo->GetImplementationUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetIsTransient", pBasicInfo->GetIsTransient());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetModuleGroupGuid", GuidUtils::ToString(pBasicInfo->GetModuleGroupGuid()));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetName", pBasicInfo->GetName());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        AddRegenDataId(pBasicInfo->GetNameRdId(), parameters, L"GetNameRdId");
    }
    {
        AddRegenDataId(pBasicInfo->GetTypeIdRdId(), parameters, L"GetTypeIdRdId");
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTypeId", pBasicInfo->GetTypeId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsCopying", pBasicInfo->IsCopying());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsCreating", pBasicInfo->IsCreating());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsUnique", pBasicInfo->IsUnique());
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pBasicInfo, parameters);
    m_parametersArrayDict.emplace_back(L"IElementBasicInformation",TransferOwnership(parameters));
    return;
}

void ElementWrapper::CreateParam_IElementStatus()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    const IElementStatus* pStatus = pElem->GetStatus();
    if (pStatus == nullptr)
    {
        return;
    }
    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsCreating", pStatus->CanBeDeleted());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanBeMoved", pStatus->CanBeMoved());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanBeOffset", pStatus->CanBeOffset());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanBeRotated", pStatus->CanBeRotated());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsCopiedFromOtherDocument", pStatus->IsCopiedFromOtherDocument());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsModifiable", pStatus->IsModifiable());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsVisible", pStatus->IsVisible());
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pStatus, parameters);
    m_parametersArrayDict.emplace_back(L"IElementStatus",TransferOwnership(parameters));
    return;
}

Sample::ParametersArrayDict& Sample::ElementWrapper::GetParameters()
{
    return m_parametersArrayDict;
}

void Sample::ElementWrapper::RunSetFieldFuncs()
{
}

void ElementWrapper::CreateParam_ILayerComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const ILayerComponent* pLayerComponent = pElem->GetLayerComponent();
    if (pLayerComponent == nullptr)
    {
        return;
    }
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    const ILayer* pLayer = quick_cast<ILayer>(pDoc->GetElement(pLayerComponent->GetLayer()));
    DBG_WARN_AND_RETURN_VOID_UNLESS(pLayer, L"pLayer为空",L"GDMPLab",L"2024-12-30");
    {
        std::wstring layerStr = pLayer == nullptr ? L"nullptr" : pLayer->GetBasicInformation()->GetName();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetLayer_Name", layerStr);
        parameters.push_back(TransferOwnership(opField));
    }
    if (pLayer != nullptr)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetLayer_IsOff", pLayer->GetLayerData()->IsOff());
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pLayerComponent, parameters);
    m_parametersArrayDict.emplace_back(L"ILayerComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementTags()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    const IElementTags* pElemTags = pElem->GetTags();
    if (pElemTags == nullptr)
    {
        return;
    }
    ParametersArray parameters;
    const std::map<std::wstring, std::set<std::wstring>> tagsMap = pElemTags->Export();
    for(const auto& it : tagsMap)
    {
        std::wstring result;
        for (const auto& wstr : it.second) {
            result += wstr;
        }
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, it.first, result);
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pElemTags, parameters);
    m_parametersArrayDict.emplace_back(L"IElementTags",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementAttributes()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementAttributes* pElemAttributes = pElem->GetAttributes();
    if (pElemAttributes == nullptr)
    {
        return;
    }
    const std::vector<std::wstring> keys = pElemAttributes->GetKeys();
    for(auto& key : keys)
    {
        ElementAttributeValueType attType;
        if (!pElemAttributes->GetAttributeValueType(key, attType))
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, key, L"GetAttributeValueType失败");
            parameters.push_back(TransferOwnership(opField));
            continue;
        }
        bool flag = false;
        std::wstring rslt;
        if (attType == ElementAttributeValueType::Binary)
        {
            std::vector<Byte> v;
            flag = pElemAttributes->GetAttributeValueAsBinary(key, v);
            rslt = L"Bytes count" + v.size();
        }
        else if (attType == ElementAttributeValueType::Bool)
        {
            bool v;
            flag = pElemAttributes->GetAttributeValueAsBool(key, v);
            rslt = StringUtil::ToWString(v);
        }
        else if (attType == ElementAttributeValueType::Double)
        {
            double v;
            flag = pElemAttributes->GetAttributeValueAsDouble(key, v);
            rslt = StringUtil::ToWString(v);
        }
        else if (attType == ElementAttributeValueType::Int32)
        {
            Int32 v;
            flag = pElemAttributes->GetAttributeValueAsInt32(key, v);
            rslt = StringUtil::ToWString(v);
        }
        else if (attType == ElementAttributeValueType::Int64)
        {
            Int64 v;
            flag = pElemAttributes->GetAttributeValueAsInt64(key, v);
            rslt = StringUtil::ToWString(v);
        }
        else if (attType == ElementAttributeValueType::String)
        {
            flag = pElemAttributes->GetAttributeValueAsString(key, rslt);
        }
        else if (attType == ElementAttributeValueType::UInt32)
        {
            UInt32 v;
            flag = pElemAttributes->GetAttributeValueAsUInt32(key, v);
            rslt = StringUtil::ToWString(v);
        }
        else if (attType == ElementAttributeValueType::UInt32)
        {
            UInt32 v;
            flag = pElemAttributes->GetAttributeValueAsUInt32(key, v);
            rslt = StringUtil::ToWString(v);
        }
        if (flag)
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, key, rslt);
            parameters.push_back(TransferOwnership(opField));
        }
        else
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, key, L"GetAttributeValueAs失败");
            parameters.push_back(TransferOwnership(opField));
        }
    }
    AddDBObjectInfo(pElemAttributes, parameters);
    m_parametersArrayDict.emplace_back(L"IElementAttributes",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IExternalDataComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IExternalDataComponent* pDataComponent = pElem->GetExternalDataComponent();
    if (pDataComponent == nullptr)
    {
        return;
    }
    const std::vector<OwnerPtr<IExternalData>>& datas = pDataComponent->GetAllExternalDatas();
    int dataCount = (int)datas.size();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IExternalData_count", dataCount);
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pDataComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IExternalDataComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IExternalObject()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGenericElement* pGenericElem = quick_cast<IGenericElement>(pElem);
    if (nullptr == pGenericElem)
    {
        return;
    }
    const gcmp::IExternalObject* pExtObject = pGenericElem->GetExternalObject();
    const IDbObject* pDbObject = dynamic_cast<const IDbObject*>(pExtObject);
    if (pDbObject)
    {
        AddDBObjectInfo(pDbObject, parameters);
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"typeid", StringUtil::ToWString(typeid(*pExtObject).name()));
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IExternalObject", TransferOwnership(parameters));
    const NdbObject* pNDBObject = quick_cast<NdbObject>(pExtObject);
    if (pNDBObject)
    {
        CreateParam_NdbClassSchema(pNDBObject->GetClassSchema(), m_parametersArrayDict);
    }
    return;
}
void Sample::ElementWrapper::CreateParam_IExternalData(const OwnerPtr<gcmp::IExternalData>& opExData)
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    m_parametersArrayDict.emplace_back(L"IExternalData", TransferOwnership(parameters));
    {
        OwnerPtr<ICalculatorCollection> opCalculators = ICalculatorCollection::Create(pElem->GetDocument());
        opExData->GetCalculators(opCalculators.get());
        CreateCalculators(opCalculators, m_parametersArrayDict);
    }
    {
        std::wstring errMsg;
        bool canBeCopy = opExData->CanBeCopied(&errMsg);
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanBeCopied", canBeCopy);
            parameters.push_back(TransferOwnership(opField));
        }
        if (!canBeCopy)
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanBeCopied_errorMessage", errMsg);
            parameters.push_back(TransferOwnership(opField));
        }
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsStrongParentsNeedCopied", opExData->IsStrongParentsNeedCopied());
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(opExData.get(), parameters);
    return;
}
void ElementWrapper::CreateParam_IElementParameters()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementParameters* pElemParams = pElem->GetElementParameters();
    if (nullptr == pElemParams)
    {
        return;
    }
    AddDBObjectInfo(pElemParams, parameters);
    m_parametersArrayDict.emplace_back(L"IElementParameters",TransferOwnership(parameters));
    return;
}
void Sample::ElementWrapper::CreateParam_IElementParameterBindings(const gcmp::IElementParameterBindings* pParamBindings)
{
    for (const OwnerPtr<IParameterBinding>& opParamBinding : pParamBindings->GetParameterBindings())
    {
        ParametersArray parameters;
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTargetParameterDefinitionId", opParamBinding->GetTargetParameterDefinitionId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetSourceElementId", opParamBinding->GetSourceElementId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetSourceParameterDefinitionId", opParamBinding->GetSourceParameterDefinitionId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBindingTypeParameter", opParamBinding->IsBindingTypeParameter());
            parameters.push_back(TransferOwnership(opField));
        }
        AddDBObjectInfo(opParamBinding.get(), parameters);
        m_parametersArrayDict.emplace_back(L"IParameterBinding: " + StringUtil::ToWString(opParamBinding->GetTargetParameterDefinitionId()), TransferOwnership(parameters));
    }
}
void Sample::ElementWrapper::CreateParam_IElementParametersCustomizer(const gcmp::IElementParametersCustomizer * pParamCustomizer)
{
    ParametersArray parameters;
    AddDBObjectInfo(pParamCustomizer, parameters);
    m_parametersArrayDict.emplace_back(L"IElementParameters", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IParameter(OwnerPtr<IParameter>& opParam)
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    CreateParameter(opParam, parameters, pElem);
    m_parametersArrayDict.emplace_back(L"IParameter_" + opParam->GetName(), TransferOwnership(parameters));
    return;
}
void Sample::ElementWrapper::CreateParam_IElementParameterGroupOverride()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    const gcmp::IElementParameters* pElemParams = pElem->GetElementParameters();
    if (nullptr == pElemParams)
    {
        return;
    }
    const gcmp::IElementParameterGroupOverride* pParamGroupOverride = pElemParams->GetElementParameterGroupOverride();
    if (nullptr == pParamGroupOverride)
    {
        return;
    }
    std::vector<OwnerPtr<IParameter>> opParams = pElemParams->GetAllParameters();
    std::map<std::wstring, std::vector<UniIdentity>> originGroupNameParametersMap;
    GroupParameterUidsArray originGroupNameParametersArray;
    GroupParameterUidsArray sortedGroupNameParametersArray;
    for (auto& opParam : opParams)
    {
        std::wstring groupName = opParam->GetParameterDefinition()->GetGroupName();
        std::vector<UniIdentity> paramsInGroup;
        auto& findIt = originGroupNameParametersMap.find(groupName);
        if (findIt != originGroupNameParametersMap.end())
        {
            paramsInGroup = TransferOwnership((*findIt).second);
        }
        paramsInGroup.emplace_back(opParam->GetParameterDefinitionUid());
        originGroupNameParametersMap[groupName] = TransferOwnership(paramsInGroup);
    }
    for (auto it : originGroupNameParametersMap)
    {
        originGroupNameParametersArray.emplace_back(std::make_pair(it.first, it.second));
    }
    pParamGroupOverride->AdjustParameterGroup(originGroupNameParametersArray, sortedGroupNameParametersArray);

}
void Sample::ElementWrapper::CreateParam_IElementRegenerationComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementRegenerationComponent* pRegenComponent = pElem->GetElementRegenerationComponent();
    if (nullptr == pRegenComponent)
    {
        return;
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanHaveCalculators", pRegenComponent->CanHaveCalculators());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanHaveRegenData", pRegenComponent->CanHaveRegenData());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IElementRegenerationComponent", TransferOwnership(parameters));
    {
        OwnerPtr<ICalculatorCollection> opCalculators = ICalculatorCollection::Create(pElem->GetDocument());
        pRegenComponent->GetCalculators(opCalculators.get());
        CreateCalculators(opCalculators, m_parametersArrayDict);
    }
    AddDBObjectInfo(pRegenComponent, parameters);
    return;
}

void ElementWrapper::CreateParam_IElementDeletionComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    const IElementDeletionComponent* pDeleComponent = pElem->GetElementDeletionComponent();
    if (nullptr == pDeleComponent)
    {
        return;
    }
    ParametersArray parameters;
    AddDBObjectInfo(pDeleComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IElementDeletionComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementTransformationComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementTransformationComponent* pTrfComponent = pElem->GetElementTransformationComponent();
    if (nullptr == pTrfComponent)
    {
        return;
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanBeMoved", pTrfComponent->CanBeMoved());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanBeRotated", pTrfComponent->CanBeRotated());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"CanBeScaled", pTrfComponent->CanBeScaled());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        AddRegenDataId(IElementTransformationComponent::GetTransformedRdId(m_elementId), parameters, L"GetTransformedRdId");
    }
    m_parametersArrayDict.emplace_back(L"IElementTransformationComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementCopyPasteComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementCopyPasteComponent* pCopyPasteComponent = pElem->GetCopyPasteComponent();
    if (nullptr == pCopyPasteComponent)
    {
        return;
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsStrongParentsNeedCopied", pCopyPasteComponent->IsStrongParentsNeedCopied());
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pCopyPasteComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IElementCopyPasteComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementCopyStrategyComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementCopyStrategyComponent* pCopyStrategyComponent = pElem->GetElementCopyStrategyComponent();
    if (nullptr == pCopyStrategyComponent)
    {
        return;
    }
    AddDBObjectInfo(pCopyStrategyComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IElementCopyStrategyComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IGroupComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGroupComponent* pGroupComponent = pElem->GetGroupComponent();
    if (nullptr == pGroupComponent)
    {
        return;
    }
    AddDBObjectInfo(pGroupComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGroupComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementPosition()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementPosition* pElemPosition = pElem->GetElementPosition();
    if (nullptr == pElemPosition)
    {
        return;
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAssociatedPlaneId", IElementPosition::GetAssociatedPlaneId(pElem));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBaseAssociatedPlaneId", pElemPosition->GetBaseAssociatedPlaneId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        const Coordinate3d coord = pElemPosition->GetBasePlaneCoordinate3d();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBasePlaneCoordinate3d", coord.GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        std::vector<Coordinate3d> coords = pElemPosition->GetLocalCoordinateSystems();
        for (int i=0;i<coords.size();i++)
        {
            Coordinate3d& coord = coords[i];
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetLocalCoordinateSystems_" + StringUtil::ToWString(i), coord.GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
    }
    {
        std::vector<Coordinate3d> coords = pElemPosition->GetWorldCoordinateSystems();
        for (int i = 0; i < coords.size(); i++)
        {
            Coordinate3d& coord = coords[i];
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWorldCoordinateSystems_" + StringUtil::ToWString(i), coord.GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
    }
    {
        AddRegenDataId(pElemPosition->GetAssociatedPlaneIdRdId(), parameters, L"GetAssociatedPlaneIdRdId");
        AddRegenDataId(pElemPosition->GetTopAssociatedPlaneIdRdId(), parameters, L"GetTopAssociatedPlaneIdRdId");
        AddRegenDataId(pElemPosition->GetStartOffsetRdId(), parameters, L"GetStartOffsetRdId");
        AddRegenDataId(pElemPosition->GetEndOffsetRdId(), parameters, L"GetEndOffsetRdId");
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBaseAssociatedPlaneHeight", pElemPosition->GetBaseAssociatedPlaneHeight());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBasePlaneOffset", pElemPosition->GetBasePlaneOffset());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetControlPointCount", pElemPosition->GetControlPointCount());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        for (int i = 0; i < pElemPosition->GetControlPointCount(); i++)
        {
            Vector3d pnt = pElemPosition->GetPointByIndex(i);
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPointByIndex_" + StringUtil::ToWString(i), pnt.GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
    }
    AddDBObjectInfo(pElemPosition, parameters);
    m_parametersArrayDict.emplace_back(L"IElementPosition",TransferOwnership(parameters));
    return;
}
void Sample::ElementWrapper::CreateParam_IPositionGeometry()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementPosition* pElemPosition = pElem->GetElementPosition();
    if (nullptr == pElemPosition)
    {
        return;
    }
    const IPositionGeometry* pPosGeom = pElemPosition->GetPositionGeometry();
    if (nullptr == pPosGeom)
    {
        return;
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetControlPointCount", pPosGeom->GetControlPointCount());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        for (int i = 0; i < pPosGeom->GetControlPointCount(); i++)
        {
            Vector3d pnt = pPosGeom->GetPointByIndex(i);
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPointByIndex_" + StringUtil::ToWString(i), pnt.GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
    }
    {
        std::vector<Coordinate3d> coords = pPosGeom->GetLocalCoordinateSystems();
        for (int i = 0; i < coords.size(); i++)
        {
            Coordinate3d& coord = coords[i];
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetLocalCoordinateSystems_" + StringUtil::ToWString(i), coord.GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
    }
    {
        std::vector<Coordinate3d> coords = pPosGeom->GetWorldCoordinateSystems();
        for (int i = 0; i < coords.size(); i++)
        {
            Coordinate3d& coord = coords[i];
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWorldCoordinateSystems_" + StringUtil::ToWString(i), coord.GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
    }
    {
        const IGeometry* pGeom = pPosGeom->GetGeometry();
        std::wstring geomStr = pGeom == nullptr ? L"nullptr" : StringUtil::ToWString(typeid(*pGeom).name());
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetGeometry", geomStr);
        parameters.push_back(TransferOwnership(opField));
    }

    AddDBObjectInfo(pPosGeom, parameters);
    m_parametersArrayDict.emplace_back(L"IPositionGeometry", TransferOwnership(parameters));

    CreateParam_IPositionCurve2d(pPosGeom);
    CreateParam_IPositionPoint2d(pPosGeom);
    CreateParam_IPositionPointOnCurve2d(pPosGeom);
    CreateParam_IPositionPointRelativeHostPoint2d(pPosGeom);
    CreateParam_IPositionPolyCurve2d(pPosGeom);
    return;
}

void Sample::ElementWrapper::CreateParam_IPositionCurve2d(const IPositionGeometry* pPosGeom)
{
    ParametersArray parameters;
    const IPositionCurve2d* pPositionCurve2d = quick_cast<IPositionCurve2d>(pPosGeom);
    if (!pPositionCurve2d) return;
    const ICurve2d* pCurve2d = pPositionCurve2d->GetBaseCurve();
    if (pCurve2d)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBaseCurve", StringUtil::ToWString(typeid(*pCurve2d).name()));
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IPositionCurve2d", TransferOwnership(parameters));
}

void Sample::ElementWrapper::CreateParam_IPositionPoint2d(const IPositionGeometry* pPosGeom)
{
    ParametersArray parameters;
    const IPositionPoint2d* pPositionPoint2d = quick_cast<IPositionPoint2d>(pPosGeom);
    if (!pPositionPoint2d) return;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetOrigin", pPositionPoint2d->GetOrigin().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDirectionX", pPositionPoint2d->GetDirectionX().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDirectionY", pPositionPoint2d->GetDirectionY().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IPositionPoint2d", TransferOwnership(parameters));
}

void Sample::ElementWrapper::CreateParam_IPositionPointOnCurve2d(const IPositionGeometry* pPosGeom)
{
    ParametersArray parameters;
    const IPositionPointOnCurve2d* pPositionPointOnCurve2d = quick_cast<IPositionPointOnCurve2d>(pPosGeom);
    if (!pPositionPointOnCurve2d) return;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetParameterOnBaseCurve", pPositionPointOnCurve2d->GetParameterOnBaseCurve());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IPositionPointOnCurve2d", TransferOwnership(parameters));
}

void Sample::ElementWrapper::CreateParam_IPositionPointRelativeHostPoint2d(const IPositionGeometry* pPosGeom)
{
    ParametersArray parameters;
    const IPositionPointRelativeHostPoint2d* pPositionPointRelativeHostPoint2d = quick_cast<IPositionPointRelativeHostPoint2d>(pPosGeom);
    if (!pPositionPointRelativeHostPoint2d) return;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetOrigin", pPositionPointRelativeHostPoint2d->GetOrigin().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWorldOrigin", pPositionPointRelativeHostPoint2d->GetWorldOrigin().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDirectionX", pPositionPointRelativeHostPoint2d->GetDirectionX().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDirectionY", pPositionPointRelativeHostPoint2d->GetDirectionY().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IPositionPointRelativeHostPoint2d", TransferOwnership(parameters));
}

void Sample::ElementWrapper::CreateParam_IPositionPolyCurve2d(const IPositionGeometry* pPosGeom)
{
    ParametersArray parameters;
    const IPositionPolyCurve2d* pPositionPolyCurve2d = quick_cast<IPositionPolyCurve2d>(pPosGeom);
    if (!pPositionPolyCurve2d) return;
    const IPolyCurve* pPolyCurve = pPositionPolyCurve2d->GetBaseCurve();
    if (pPolyCurve)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBaseCurve", StringUtil::ToWString(typeid(*pPolyCurve).name()));
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IPositionPolyCurve2d", TransferOwnership(parameters));
}

void Sample::ElementWrapper::CreateParam_IPositionAssociatedCoordinate(const IPositionAssociatedPlane* pPosPlane)
{
    ParametersArray parameters;
    const IPositionAssociatedCoordinate* pPositionAssociatedCoordinate = quick_cast<IPositionAssociatedCoordinate>(pPosPlane);
    if (pPositionAssociatedCoordinate)
    {
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCoordinate3d", pPositionAssociatedCoordinate->GetCoordinate3d().GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetOffset", pPositionAssociatedCoordinate->GetOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsRightHandRuleMust", pPositionAssociatedCoordinate->IsRightHandRuleMust());
            parameters.push_back(TransferOwnership(opField));
        }
        m_parametersArrayDict.emplace_back(L"IElementPositionPoints", TransferOwnership(parameters));
    }
}

void Sample::ElementWrapper::CreateParam_IPositionSingleAssociatedPlane(const IPositionAssociatedPlane* pPosPlane)
{
    ParametersArray parameters;
    const IPositionSingleAssociatedPlane* pPositionSingleAssociatedPlane = quick_cast<IPositionSingleAssociatedPlane>(pPosPlane);
    if (pPositionSingleAssociatedPlane)
    {
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAssociatedPlaneId", pPositionSingleAssociatedPlane->GetAssociatedPlaneId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetOffset", pPositionSingleAssociatedPlane->GetOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsAllowRemoveAssociatedPlane", pPositionSingleAssociatedPlane->IsAllowRemoveAssociatedPlane());
            parameters.push_back(TransferOwnership(opField));
        }
        m_parametersArrayDict.emplace_back(L"IPositionSingleAssociatedPlane", TransferOwnership(parameters));
    }
}

void Sample::ElementWrapper::CreateParam_IPositionSingleAssociatedPlaneWithTwoOffsets(const IPositionAssociatedPlane* pPosPlane)
{
    ParametersArray parameters;
    const IPositionSingleAssociatedPlaneWithTwoOffsets* pPositionSingleAssociatedPlaneWithTwoOffsets = quick_cast<IPositionSingleAssociatedPlaneWithTwoOffsets>(pPosPlane);
    if (pPositionSingleAssociatedPlaneWithTwoOffsets)
    {
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAssociatedPlaneId", pPositionSingleAssociatedPlaneWithTwoOffsets->GetAssociatedPlaneId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAssociatedPlaneCoordinate3d", pPositionSingleAssociatedPlaneWithTwoOffsets->GetAssociatedPlaneCoordinate3d().GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAssociatedPlaneOffsetParameterUid", pPositionSingleAssociatedPlaneWithTwoOffsets->GetAssociatedPlaneOffsetParameterUid().GetGuidAndDebugString());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetAssociatedPlaneParameterUid", pPositionSingleAssociatedPlaneWithTwoOffsets->GetAssociatedPlaneParameterUid().GetGuidAndDebugString());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStartOffsetParameterUid", pPositionSingleAssociatedPlaneWithTwoOffsets->GetStartOffsetParameterUid().GetGuidAndDebugString());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEndOffsetParameterUid", pPositionSingleAssociatedPlaneWithTwoOffsets->GetEndOffsetParameterUid().GetGuidAndDebugString());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStartOffset", pPositionSingleAssociatedPlaneWithTwoOffsets->GetStartOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEndOffset", pPositionSingleAssociatedPlaneWithTwoOffsets->GetEndOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsAllowAssociatedPlaneOffsetParameter", pPositionSingleAssociatedPlaneWithTwoOffsets->IsAllowAssociatedPlaneOffsetParameter());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsStayHorizontal", pPositionSingleAssociatedPlaneWithTwoOffsets->IsStayHorizontal());
            parameters.push_back(TransferOwnership(opField));
        }
        m_parametersArrayDict.emplace_back(L"IPositionSingleAssociatedPlaneWithTwoOffsets", TransferOwnership(parameters));
    }
}

void Sample::ElementWrapper::CreateParam_IPositionTwoAssociatedPlanes(const IPositionAssociatedPlane* pPosPlane)
{
    ParametersArray parameters;
    const IPositionTwoAssociatedPlanes* pPositionTwoAssociatedPlanes = quick_cast<IPositionTwoAssociatedPlanes>(pPosPlane);
    if (pPositionTwoAssociatedPlanes)
    {
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBottomAssociatedPlaneId", pPositionTwoAssociatedPlanes->GetBottomAssociatedPlaneId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTopAssociatedPlaneId", pPositionTwoAssociatedPlanes->GetTopAssociatedPlaneId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBottomOffset", pPositionTwoAssociatedPlanes->GetBottomOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTopOffset", pPositionTwoAssociatedPlanes->GetTopOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTopAssociatedPlaneHeight", pPositionTwoAssociatedPlanes->GetTopAssociatedPlaneHeight());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHeight", pPositionTwoAssociatedPlanes->GetHeight());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"ShouldCalculateHeight", pPositionTwoAssociatedPlanes->ShouldCalculateHeight());
            parameters.push_back(TransferOwnership(opField));
        }
        m_parametersArrayDict.emplace_back(L"IPositionTwoAssociatedPlanes", TransferOwnership(parameters));
    }
}

void Sample::ElementWrapper::CreateParam_IPositionTwoAssociatedPlanesWithFourOffsets(const IPositionAssociatedPlane* pPosPlane)
{
    ParametersArray parameters;
    const IPositionTwoAssociatedPlanesWithFourOffsets* pPositionTwoAssociatedPlanesWithFourOffsets = quick_cast<IPositionTwoAssociatedPlanesWithFourOffsets>(pPosPlane);
    if (pPositionTwoAssociatedPlanesWithFourOffsets)
    {
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBottomAssociatedPlaneId", pPositionTwoAssociatedPlanesWithFourOffsets->GetBottomAssociatedPlaneId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTopAssociatedPlaneId", pPositionTwoAssociatedPlanesWithFourOffsets->GetTopAssociatedPlaneId());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStartBottomOffset", pPositionTwoAssociatedPlanesWithFourOffsets->GetStartBottomOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStartTopOffset", pPositionTwoAssociatedPlanesWithFourOffsets->GetStartTopOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStartHeight", pPositionTwoAssociatedPlanesWithFourOffsets->GetStartHeight());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEndBottomOffset", pPositionTwoAssociatedPlanesWithFourOffsets->GetEndBottomOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEndTopOffset", pPositionTwoAssociatedPlanesWithFourOffsets->GetEndTopOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetEndHeight", pPositionTwoAssociatedPlanesWithFourOffsets->GetEndHeight());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTopOffset", pPositionTwoAssociatedPlanesWithFourOffsets->GetTopOffset());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"ShouldCalculateHeight", pPositionTwoAssociatedPlanesWithFourOffsets->ShouldCalculateHeight());
            parameters.push_back(TransferOwnership(opField));
        }
        m_parametersArrayDict.emplace_back(L"IPositionTwoAssociatedPlanesWithFourOffsets", TransferOwnership(parameters));
    }
}

void Sample::ElementWrapper::CreateParam_IPositionAssociatedPlane()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementPosition* pElemPosition = pElem->GetElementPosition();
    if (nullptr == pElemPosition)
    {
        return;
    }
    const IPositionAssociatedPlane* pPosPlane = pElemPosition->GetPositionAssociatedPlane();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBasePlaneCoordinate3d", pPosPlane->GetBasePlaneCoordinate3d().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBasePlaneElementId", pPosPlane->GetBasePlaneElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBasePlaneHeight", pPosPlane->GetBasePlaneHeight());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBasePlaneOffset", pPosPlane->GetBasePlaneOffset());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTopAssociatedPlaneId", IPositionAssociatedPlane::GetTopAssociatedPlaneId(pElem));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        double offset = 0;
        IPositionAssociatedPlane::GetBottomPlaneHeightOffset(pElem, offset);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBottomPlaneHeightOffset", offset);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        double offset = 0;
        IPositionAssociatedPlane::GetTopPlaneHeightOffset(pElem, offset);
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTopPlaneHeightOffset", offset);
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pPosPlane, parameters);
    m_parametersArrayDict.emplace_back(L"IElementPositionPoints", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementPositionPoints()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementPositionPoints* pPosPoints = pElem->GetPositionPoints();
    if (nullptr == pPosPoints)
    {
        return;
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetControlPointCount", pPosPoints->GetControlPointCount());
        parameters.push_back(TransferOwnership(opField));
    }
    for (int i = 0; i < pPosPoints->GetControlPointCount(); i++)
    {
        Vector3d pnt = pPosPoints->GetControlPoint(i);
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetControlPoint_" + StringUtil::ToWString(i), pnt.GetStringForDebug());
            parameters.push_back(TransferOwnership(opField));
        }
        {
            AddRegenDataId(pPosPoints->GetControlPointRdId(i), parameters, L"GetControlPointRdId_" + StringUtil::ToWString(i));
        }
    }
    AddDBObjectInfo(pPosPoints, parameters);
    m_parametersArrayDict.emplace_back(L"IElementPositionPoints",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementPositionReportComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementPositionReportComponent* pElemPosReporter = pElem->GetElementPositionReportComponent();
    if (nullptr == pElemPosReporter)
    {
        return;
    }
    Coordinate3d coord;
    if(pElemPosReporter->GetCoordinate3d(coord))
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCoordinate3d", coord.GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    double height = 0;
    if (pElemPosReporter->GetHeight(height))
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHeight", height);
        parameters.push_back(TransferOwnership(opField));
    }
    std::vector<RegenDataId> dataIds;
    if (pElemPosReporter->GetReferencePlaneRdId(dataIds))
    {
        for (int i = 0; i < dataIds.size(); i++)
        {
            AddRegenDataId(dataIds[i], parameters, L"GetReferencePlaneRdId_" + StringUtil::ToWString(i));
        }
    }
    AddDBObjectInfo(pElemPosReporter, parameters);
    m_parametersArrayDict.emplace_back(L"IElementPositionReportComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementModelShape()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementModelShape* pElemModelShape = pElem->GetElementModelShape();
    if (nullptr == pElemModelShape)
    {
        return;
    }
    AddRegenDataId(pElemModelShape->GetGraphicsElementShapeRdId(), parameters, L"GetGraphicsElementShapeRdId");
    AddDBObjectInfo(pElemModelShape, parameters);
    m_parametersArrayDict.emplace_back(L"IElementModelShape",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementViewSpecificShapeComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementViewSpecificShapeComponent* pViewSpeicifcComponent = pElem->GetViewSpecificShapeComponent();
    if (nullptr == pViewSpeicifcComponent)
    {
        return;
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsInvisibleInAllModelViews", pViewSpeicifcComponent->IsInvisibleInAllModelViews());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsInvisibleInAllModelViews", pViewSpeicifcComponent->IsInvisibleInAllModelViews());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsMultiAnnotation", pViewSpeicifcComponent->IsMultiAnnotation());
        parameters.push_back(TransferOwnership(opField));
    }
    std::vector<ElementId> dependentElementIds;
    pViewSpeicifcComponent->ReportDependentElements(dependentElementIds);
    for (int i = 0; i < dependentElementIds.size(); i++)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"ReportDependentElements_" + StringUtil::ToWString(i), 
            dependentElementIds[i]);
        parameters.push_back(TransferOwnership(opField));
    }
    IModelView* pModelView = UiDocumentViewUtils::GetCurrentModelView();
    if (pModelView)
    {
        OwnerPtr<IModelViewDataAccessor> opViewData = IModelViewDataAccessor::Create(pModelView, pElem);
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsVisibleInModelView_IModelView", pViewSpeicifcComponent->IsVisibleInModelView(pModelView));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsVisibleInModelView_IModelViewDataAccessor", pViewSpeicifcComponent->IsVisibleInModelView(*opViewData));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsCuttableByModelViewRange_IModelView", pViewSpeicifcComponent->IsCuttableByModelViewRange(pModelView));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsCuttableByModelViewRange_IModelViewDataAccessor", pViewSpeicifcComponent->IsCuttableByModelViewRange(*opViewData));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsLightingEnabled_IModelView", pViewSpeicifcComponent->IsLightingEnabled(pModelView, true));
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsLightingEnabled_IModelViewDataAccessor", pViewSpeicifcComponent->IsLightingEnabled(*opViewData, true));
            parameters.push_back(TransferOwnership(opField));
        }
        const IRenderPriorityOffsetProvider* pOffsetProvider = pViewSpeicifcComponent->GetRenderPriorityOffsetProvider();
        if (pOffsetProvider)
        {
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetRenderPriorityOffsetProvider", StringUtil::ToWString(typeid(pOffsetProvider).name()));
                parameters.push_back(TransferOwnership(opField));
            }
            bool isOffsetValid = false;
            Byte offset = 0;
            pOffsetProvider->GetRenderPriorityOffset(pModelView, *pElem, isOffsetValid, offset);
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetRenderPriorityOffset_isOffsetValid", isOffsetValid);
                parameters.push_back(TransferOwnership(opField));
            }
            {
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetRenderPriorityOffset_offset", offset);
                parameters.push_back(TransferOwnership(opField));
            }
        }
        else
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetRenderPriorityOffsetProvider", L"nullptr");
            parameters.push_back(TransferOwnership(opField));
        }
        const IGraphicsNodeVisibilityCustomizer* pVisCustomizer = pViewSpeicifcComponent->GetGraphicsNodeVisibilityCustomizer();
        if (pVisCustomizer)
        {
            const IGraphicsNodeVisibilityCustomizer::CustomizationInfo customizerInfo = pVisCustomizer->GetCustomizationInfo(pModelView);
            const std::map<std::vector<GraphicsNodeId>, GraphicsNodeVisibility> visMap = customizerInfo.GetCustomizedElementVisibilitiesByGraphicsNodeIdPaths();
            for (auto& it : visMap)
            {
                std::wstring txt;
                for (auto& nodeId : it.first)
                {
                    txt += StringUtil::ToWString(nodeId.AsInt()) + L"-";
                }
                std::wstring visName;
                switch (it.second)
                {
                case GraphicsNodeVisibility::Always: visName = L"Always"; break;
                case GraphicsNodeVisibility::HighlightedExactly: visName = L"HighlightedExactly"; break;
                case GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly: visName = L"HighlightedExactly_Or_SelectedExactly"; break;
                case GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlighted: visName = L"HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlighted"; break;
                case GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected: visName = L"HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected"; break;
                case GraphicsNodeVisibility::Never: visName = L"Never"; break;
                case GraphicsNodeVisibility::NeverHighlighted: visName = L"NeverHighlighted"; break;
                case GraphicsNodeVisibility::NeverHighlighted_Or_Selected: visName = L"NeverHighlighted_Or_Selected"; break;
                default:
                    break;
                }
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, txt, visName);
                parameters.push_back(TransferOwnership(opField));
            }
        }
        else
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetGraphicsNodeVisibilityCustomizer", L"nullptr");
            parameters.push_back(TransferOwnership(opField));
        }
    }
    AddDBObjectInfo(pViewSpeicifcComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IElementViewSpecificShapeComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IGraphicsNodeStyleAndMaterialOverride()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGraphicsNodeStyleAndMaterialOverride* pStyleMatOverride = pElem->GetShapeGraphicsNodeStyleAndMaterialOverrideComponent();
    if (pStyleMatOverride == nullptr)
    {
        return;
    }
    {
        PolygonOffsetMode offsetMode = pStyleMatOverride->GetPolygonOffsetMode();
        std::wstring offsetModeName;
        switch (offsetMode)
        {
        case PolygonOffsetMode::Custom : offsetModeName = L"Custom"; break;
        case PolygonOffsetMode::LessThanNormal: offsetModeName = L"LessThanNormal"; break;
        case PolygonOffsetMode::MoreThanNormal: offsetModeName = L"MoreThanNormal"; break;
        case PolygonOffsetMode::None: offsetModeName = L"None"; break;
        case PolygonOffsetMode::Normal: offsetModeName = L"Normal"; break;
        default:
            break;
        }
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPolygonOffsetMode", offsetModeName);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        float factor, units;
        pStyleMatOverride->GetPolygonOffsetCustom(factor, units);
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPolygonOffsetCustom_factor", factor);
            parameters.push_back(TransferOwnership(opField));
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPolygonOffsetCustom_units", units);
            parameters.push_back(TransferOwnership(opField));
        }
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsWireFrameModeEnabled", pStyleMatOverride->IsWireFrameModeEnabled());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsHiddenLineModeEnabled", pStyleMatOverride->IsHiddenLineModeEnabled());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetRenderPatchCombineId", StringUtil::ToWString(pStyleMatOverride->GetRenderPatchCombineId()));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementUnderLayUpLayInfo", pStyleMatOverride->GetElementUnderLayUpLayInfo());
        parameters.push_back(TransferOwnership(opField));
    }
    AddDBObjectInfo(pStyleMatOverride, parameters);
    m_parametersArrayDict.emplace_back(L"IGraphicsNodeStyleAndMaterialOverride",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IElementModelingOperations()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementModelingOperations* pModelingOperationComponent = pElem->GetModelingOperations();
    if (pModelingOperationComponent == nullptr)
    {
        return;
    }
    const std::vector<const IModelingOperation*> pModelingOperations = pModelingOperationComponent->GetModelingOperations();
    for (int i = 0; i < pModelingOperations.size(); i++)
    {
        const IModelingOperation* pModelingOperation = pModelingOperations[i];
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetOperationId_" + StringUtil::ToWString(i), pModelingOperation->GetOperationId());
            parameters.push_back(TransferOwnership(opField));
        }
        const std::unordered_map<int, int> childToParentIdMap = pModelingOperation->GetChildToParentIdMap();
        int index = 0;
        for(auto& it : childToParentIdMap)
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetChildToParentIdMap_" + StringUtil::ToWString(i) + L"_" + StringUtil::ToWString(index), 
                StringUtil::ToWString(it.first) + L"-" + StringUtil::ToWString(it.second));
            parameters.push_back(TransferOwnership(opField));
            index++;
        }
        {
            OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"typeid_" + StringUtil::ToWString(i), StringUtil::ToWString(typeid(*pModelingOperation).name()));
            parameters.push_back(TransferOwnership(opField));
        }
    }
    AddDBObjectInfo(pModelingOperationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IElementModelingOperations",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IGeometryRelationshipComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IOpeningRelationshipBehavior()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    const IOpeningRelationshipBehavior* pOpeningRelationship = pGeomRelationComponent->GetOpeningRelationship();
    if (nullptr == pOpeningRelationship)
    {
        return;
    }
    const std::vector<ElementId> openingIds = pOpeningRelationship->GetElementOpeningIds();
    int i = 0;
    for (auto& id : openingIds)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementOpeningIds_" + StringUtil::ToWString(i), id);
        parameters.push_back(TransferOwnership(opField));
        i++;
    }
    i = 0;
    const std::vector<RegenDataId> openingRdIds = pOpeningRelationship->GetElementOpeningRdIds();
    for (auto& rdId : openingRdIds)
    {
        AddRegenDataId(rdId, parameters, L"GetElementOpeningRdIds_" + StringUtil::ToWString(i));
        i++;
    }
    i = 0;
    const std::vector<RegenDataId> openedRdIds = pOpeningRelationship->GetElementOpenedRdIds();
    for (auto& rdId : openedRdIds)
    {
        AddRegenDataId(rdId, parameters, L"GetElementOpenedRdIds_" + StringUtil::ToWString(i));
        i++;
    }
    AddRegenDataId(pOpeningRelationship->GetElementOpeningIdsRdId(), parameters, L"GetElementOpeningIdsRdId");
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IOpenedGraphicsElementShapeComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    const IOpenedGraphicsElementShapeComponent* pOpenedGRep = pGeomRelationComponent->GetOpenedGraphicsElementShapeComponent();
    if (nullptr == pOpenedGRep)
    {
        return;
    }
    const IGraphicsElementShape* pGRep = pOpenedGRep->GetOpenedGraphicsElementShapeForOpeningElementCalculator();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetOpenedGraphicsElementShapeForOpeningElementCalculator", 
            pGRep == nullptr ? L"nullptr" : L"GetBox:" + pGRep->GetBox().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    AddRegenDataId(pOpenedGRep->GetOpenedGraphicsElementShapeForOpeningElementCalculatorRdId(), parameters, 
        L"GetOpenedGraphicsElementShapeForOpeningElementCalculatorRdId");
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IOpeningGraphicsElementShapeComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    const IOpeningGraphicsElementShapeComponent* pOpeningGRepComponent = pGeomRelationComponent->GetOpeningGraphicsElementShapeComponent();
    if (nullptr == pOpeningGRepComponent)
    {
        return;
    }
    const IGraphicsElementShape* pOpeningGRep = pOpeningGRepComponent->GetOpeningGraphicsElementShape();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetOpeningGraphicsElementShape",
            pOpeningGRep == nullptr ? L"nullptr" : L"GetBox:" + pOpeningGRep->GetBox().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    const IGraphicsElementShape* pJoinedCutterGRep = pOpeningGRepComponent->GetJoinedCutterGraphicsElementShape();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetJoinedCutterGraphicsElementShape",
            pJoinedCutterGRep == nullptr ? L"nullptr" : L"GetBox:" + pJoinedCutterGRep->GetBox().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"ShouldCalculateIntersectingGraphicsElementShape",
            pOpeningGRepComponent->ShouldCalculateIntersectingGraphicsElementShape());
        parameters.push_back(TransferOwnership(opField));
    }
    AddRegenDataId(pOpeningGRepComponent->GetOpeningGraphicsElementShapeRdId(), parameters,
        L"GetOpenedGraphicsElementShapeForOpeningElementCalculatorRdId");
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_ICutRelationshipBehavior()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    const ICutRelationshipBehavior* pCutRelationship = pGeomRelationComponent->GetCutRelationship();
    if (nullptr == pCutRelationship)
    {
        return;
    }
    const std::vector<ElementId> openingIds = pCutRelationship->GetElementCutIds();
    int i = 0;
    for (auto& id : openingIds)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementCutIds_" + StringUtil::ToWString(i), id);
        parameters.push_back(TransferOwnership(opField));
        i++;
    }
    const std::vector<RegenDataId> openedRdIds = pCutRelationship->GetElementCutRdIds();
    for (auto& rdId : openedRdIds)
    {
        AddRegenDataId(rdId, parameters, L"GetElementCutRdIds_" + StringUtil::ToWString(i));
        i++;
    }
    AddRegenDataId(pCutRelationship->GetCutElementIdsRdId(), parameters, L"GetCutElementIdsRdId");
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_ICutterGraphicsElementShapeComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    const ICutterGraphicsElementShapeComponent* pCutterGRep = pGeomRelationComponent->GetCutterGraphicsElementShapeComponent();
    if (nullptr == pCutterGRep)
    {
        return;
    }
    const IGraphicsElementShape* pGRep = pCutterGRep->GetCutterGraphicsElementShape();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCutterGraphicsElementShape",
            pGRep == nullptr ? L"nullptr" : L"GetBox:" + pGRep->GetBox().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    int i = 0;
    const std::vector<RegenDataId> cutterRdIds = pCutterGRep->GetCutterGraphicsElementShapeRdIds();
    for (auto& rdId : cutterRdIds)
    {
        AddRegenDataId(rdId, parameters, L"GetCutterGraphicsElementShapeRdIds)" + StringUtil::ToWString(i));
        i++;
    }
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_ICutteeGraphicsElementShapeComponent()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    const ICutteeGraphicsElementShapeComponent* pCutteeGRep = pGeomRelationComponent->GetCutteeGraphicsElementShapeComponent();
    AddRegenDataId(pCutteeGRep->GetPreCutteeGraphicsElementShapeRdId(), parameters, L"GetCutteeGraphicsElementShapeComponent");
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IJoinRelationshipBehavior()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    const IJoinRelationshipBehavior* pJoinRelationship = pGeomRelationComponent->GetJoinRelationship();
    if (nullptr == pJoinRelationship)
    {
        return;
    }
    int i = 0;
    const std::vector<ElementId> joinStartIds = pJoinRelationship->GetElementJoinIdsAtStart();
    for (auto& id : joinStartIds)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementJoinIdsAtStart_" + StringUtil::ToWString(i), id);
        parameters.push_back(TransferOwnership(opField));
        i++;
    }
    i = 0;
    const std::vector<ElementId> joinEndIds = pJoinRelationship->GetElementJoinIdsAtEnd();
    for (auto& id : joinEndIds)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementJoinIdsAtEnd_" + StringUtil::ToWString(i), id);
        parameters.push_back(TransferOwnership(opField));
        i++;
    }
    i = 0;
    const std::vector<ElementId> joinMidIds = pJoinRelationship->GetElementJoinIdsAtMiddle();
    for (auto& id : joinMidIds)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementJoinIdsAtMiddle_" + StringUtil::ToWString(i), id);
        parameters.push_back(TransferOwnership(opField));
        i++;
    }
    AddRegenDataId(pJoinRelationship->GetElementJoinIdRdId(), parameters, L"GetElementJoinIdRdId");
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent", TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_IGeometryRelationship(int index)
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IGeometryRelationshipComponent* pGeomRelationComponent = pElem->GetGeometryRelationshipComponent();
    if (nullptr == pGeomRelationComponent)
    {
        return;
    }
    const IGeometryRelationship* pGeomRelationship = pGeomRelationComponent->GetGeometryRelationshipByIndex(index);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGeomRelationship, L"pGeomRelationship为空",L"GDMPLab",L"2024-12-30");
    const IGraphicsElementShape* pGRep = pGeomRelationship->GetGraphicsElementShape();
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetGraphicsElementShape",
            pGRep == nullptr ? L"nullptr" : L"GetBox:" + pGRep->GetBox().GetStringForDebug());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetPriority", pGeomRelationship->GetPriority());
        parameters.push_back(TransferOwnership(opField));
    }
    OwnerPtr<ICalculatorCollection> opCalculators = ICalculatorCollection::Create(pElem->GetDocument());
    pGeomRelationship->GetCalculators(opCalculators.get());
    CreateCalculators(opCalculators, m_parametersArrayDict);
    int i = 0;
    const std::set<ElementId> hideEdgeIds = pGeomRelationship->GetElementIdsToHideEdge();
    for (auto& id : hideEdgeIds)
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementIdsToHideEdge_" + StringUtil::ToWString(i), id);
        parameters.push_back(TransferOwnership(opField));
        i++;
    }
    AddRegenDataId(pGeomRelationship->GetGeometryRelationshipRdId(), parameters, L"GetGeometryRelationshipRdId");
    AddDBObjectInfo(pGeomRelationComponent, parameters);
    m_parametersArrayDict.emplace_back(L"IGeometryRelationshipComponent", TransferOwnership(parameters));
    return;
}

void ElementWrapper::CreateParam_IElementConstraintBehavior()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const IElementConstraintBehavior* pConstraintsBehavior = pElem->GetElementConstraintBehavior();
    if (nullptr == pConstraintsBehavior)
    {
        return;
    }
    AddDBObjectInfo(pConstraintsBehavior, parameters);
    m_parametersArrayDict.emplace_back(L"IElementConstraintBehavior",TransferOwnership(parameters));
    return;
}
void ElementWrapper::CreateParam_ICommandBehavior()
{
    const IElement* pElem = GetElement();
    if (nullptr == pElem)
    {
        return;
    }
    ParametersArray parameters;
    const ICommandBehavior* pCmdBehavior = pElem->GetCommandBehavior();
    if (nullptr == pCmdBehavior)
    {
        return;
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDoubleClickCommandType", pCmdBehavior->GetDoubleClickCommandType());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHandleCommandType", pCmdBehavior->GetHandleCommandType());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"ICommandBehavior",TransferOwnership(parameters));
    return;
}

void Sample::ElementWrapper::CreateParam_IInstance()
{
    const IInstance* pInstance = GetInstance();
    if (nullptr == pInstance)
    {
        return;
    }
    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHostElementId", pInstance->GetHostElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHostInstanceId", pInstance->GetHostInstanceId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsHostWeakParent", pInstance->IsHostWeakParent());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetSymbolGraphicsElementShapeHolderId", pInstance->GetSymbolGraphicsElementShapeHolderId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCutterGraphicsElementShapeHolderId", pInstance->GetCutterGraphicsElementShapeHolderId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsBoundWithPlane", pInstance->IsBoundWithPlane());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetHostModelViewId", pInstance->GetHostModelViewId());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IInstance", TransferOwnership(parameters));

}

void Sample::ElementWrapper::CreateParam_IInstance_IInstanceType()
{
    const IInstance* pInstance = GetInstance();
    if (nullptr == pInstance)
    {
        return;
    }
    const IElementBasicInformation* pElemBasicInfo = pInstance->GetBasicInformation();
    const IInstanceType* pInstanceType = quick_cast<IInstanceType>(pElemBasicInfo->GetType());
    CreateParam_IInstance_IFamily_InstanceType(pInstanceType);
}
void Sample::ElementWrapper::CreateParam_IInstance_IFamily()
{
    const IInstance* pInstance = GetInstance();
    if (nullptr == pInstance)
    {
        return;
    }
    const IFamily* pFamily = pInstance->GetFamily();
    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementId", pFamily->GetElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsInplaceEditFamily", pFamily->IsInplaceEditFamily());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFamilyServiceUid", pFamily->GetFamilyServiceUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFamilyThumbnailPath", pFamily->GetFamilyThumbnailPath());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetScale", pFamily->GetScale());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IFamily", TransferOwnership(parameters));
}
void Sample::ElementWrapper::CreateParam_IInstance_IFamilyManager()
{
    const IInstance* pInst = GetInstance();
    if (nullptr == pInst)
    {
        return;
    }
    const IDocument* pDoc = pInst->GetDocument();
    const gcmp::IFamily* pFamily = pInst->GetFamily();
    const IFamilyConfigElement* pIFamilyConfigElement = IFamilyConfigElement::GetFamilyConfigElementByName(pDoc, pFamily->GetBasicInformation()->GetName());

    std::wstring familyFilePath = pIFamilyConfigElement->GetFamilyFilePath(pDoc);
    IDocumentManager* pDocManager = IDocumentManager::Get();
    DocumentLoadOptions docLoadOptions(false, false, true, false);
    DocumentLoadResult docLoadResult;
    std::set<std::wstring> unrecognizedAssemblies;
    IDocument* pFamDoc = pDocManager->OpenDocument(familyFilePath, docLoadOptions, docLoadResult, nullptr, &unrecognizedAssemblies);
    const gfam::IFamilyManager* pFamMgr = gfam::IFamilyManager::Get(pFamDoc);

    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementId", pFamMgr->GetElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCurrentFamilyTypeName", pFamMgr->GetCurrentFamilyTypeName());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        AddRegenDataId(pFamMgr->GetCurrentFamilyTypeNameRdId(), parameters, L"GetCurrentFamilyTypeNameRdId");
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetScale", pFamMgr->GetScale());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsEnableViewAngleTolerance", pFamMgr->IsEnableViewAngleTolerance());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetBuiltInProfileInstanceType", pFamMgr->GetBuiltInProfileInstanceType());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IFamilyManager", TransferOwnership(parameters));

    IDocumentManager::Get()->CloseDocument(pFamDoc->GetRuntimeId());
}
void Sample::ElementWrapper::CreateParam_IInstance_IElementParameters(const gcmp::IElementParameters* pElemParams)
{
    ParametersArray parameters;
    if (nullptr == pElemParams)
    {
        return;
    }
    AddDBObjectInfo(pElemParams, parameters);
    m_parametersArrayDict.emplace_back(L"IElementParameters", TransferOwnership(parameters));
}
void Sample::ElementWrapper::CreateParam_IInstance_IFamilyConfigElement()
{
    const IInstance* pInst = GetInstance();
    if (nullptr == pInst)
    {
        return;
    }
    const IDocument* pDoc = pInst->GetDocument();
    const gcmp::IFamily* pFamily = pInst->GetFamily();
    const IFamilyConfigElement* pIFamilyConfigElement = IFamilyConfigElement::GetFamilyConfigElementByName(pDoc, pFamily->GetBasicInformation()->GetName());

    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementId", pIFamilyConfigElement->GetElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCurrentFamilyTypeName", pIFamilyConfigElement->GetFamilyFileName());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetScale", pIFamilyConfigElement->GetFamilyFilePath(pDoc));
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFamilyUid", pIFamilyConfigElement->GetFamilyUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IFamilyConfigElement", TransferOwnership(parameters));
}

void Sample::ElementWrapper::CreateParam_IInstance_IElementParameters_IParameter(OwnerPtr<gcmp::IParameter> opParam)
{
    ParametersArray parameters;
    CreateParameter(opParam, parameters, GetElement());
    m_parametersArrayDict.emplace_back(L"IParameter", TransferOwnership(parameters));
}
void Sample::ElementWrapper::CreateParam_IInstance_IFamilyParameterDefinition_IParameter(IDocument* pFamDoc, const gfam::IFamilyParameterDefinition* pFamParamDef)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(pFamParamDef, L"pFamParamDef为空",L"GDMPLab",L"2024-12-30");
    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementId", pFamParamDef->GetElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetUid", pFamParamDef->GetUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        std::wstring rslt;
        switch (pFamParamDef->GetParameterType())
        {
        case gfam::FamilyParameterType::Angle: rslt = L"Angle"; break;
        case gfam::FamilyParameterType::Area: rslt = L"Area"; break;
        case gfam::FamilyParameterType::Bool: rslt = L"Bool"; break;
        case gfam::FamilyParameterType::Double: rslt = L"Double"; break;
        case gfam::FamilyParameterType::Integer: rslt = L"Integer"; break;
        case gfam::FamilyParameterType::Internal: rslt = L"Internal"; break;
        case gfam::FamilyParameterType::Invalid: rslt = L"Invalid"; break;
        case gfam::FamilyParameterType::Length: rslt = L"Length"; break;
        case gfam::FamilyParameterType::Material: rslt = L"Material"; break;
        case gfam::FamilyParameterType::NumOfTypes: rslt = L"NumOfTypes"; break;
        case gfam::FamilyParameterType::Profile: rslt = L"Profile"; break;
        case gfam::FamilyParameterType::String: rslt = L"String"; break;
        case gfam::FamilyParameterType::TextStyle: rslt = L"TextStyle"; break;
        case gfam::FamilyParameterType::Volume: rslt = L"Volume"; break;
        default:
            break;
        }
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetParameterType", rslt);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        std::wstring rslt;
        switch (pFamParamDef->GetStorageType())
        {
        case ParameterStorageType::Bool : rslt = L"Bool"; break;
        case ParameterStorageType::BuildingStorey : rslt = L"BuildingStorey"; break;
        case ParameterStorageType::Color : rslt = L"Color"; break;
        case ParameterStorageType::Coordinates : rslt = L"Coordinates"; break;
        case ParameterStorageType::Count : rslt = L"Count"; break;
        case ParameterStorageType::Custom : rslt = L"Custom"; break;
        case ParameterStorageType::Double : rslt = L"Double"; break;
        case ParameterStorageType::ElementId : rslt = L"ElementId"; break;
        case ParameterStorageType::Geometry : rslt = L"Geometry"; break;
        case ParameterStorageType::Guid : rslt = L"Guid"; break;
        case ParameterStorageType::Int : rslt = L"Int"; break;
        case ParameterStorageType::Invalid : rslt = L"Invalid"; break;
        case ParameterStorageType::None : rslt = L"None"; break;
        case ParameterStorageType::StandardCodeReference : rslt = L"StandardCodeReference"; break;
        case ParameterStorageType::String : rslt = L"String"; break;
        default:
            break;
        }
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetStorageType", rslt);
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsDeterminedByFormula", pFamParamDef->IsDeterminedByFormula());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFormula", pFamParamDef->GetFormula());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsInstance", pFamParamDef->IsInstance());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsShared", pFamParamDef->IsShared());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsEditable", pFamParamDef->IsEditable());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        const IParameterValueList* pValueList = pFamParamDef->GetValueList();
        if (pValueList)
        {
            std::vector<std::wstring> txts = pValueList->GetDisplayStrings(pFamDoc);
            if (txts.size() > 0)
            {
                std::wstring result;
                for (const auto& wstr : txts) {
                    result += wstr;
                }
                OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetValueList", result);
                parameters.push_back(TransferOwnership(opField));
            }
        }
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetName", pFamParamDef->GetName());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetGroupName", pFamParamDef->GetGroupName());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDescription", pFamParamDef->GetDescription());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsVisible", pFamParamDef->IsVisible());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetUnitTypeId", pFamParamDef->GetUnitTypeId().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetParameterTypeId", pFamParamDef->GetParameterTypeId().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IFamilyConfigElement", TransferOwnership(parameters));
}

void Sample::ElementWrapper::CreateParam_IInstance_IFamilyManager_IFamilyType(const IFamilyType* pFamType, const gfam::IFamilyManager* pFamMgr)
{
    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetName", pFamType->GetName());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFamilyTypeUid", pFamType->GetFamilyTypeUid().GetGuidAndDebugString());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        const std::vector<OwnerPtr<IParameter>> opParams = pFamType->GetFamilyParameters();
        for (auto& it : opParams)
        {
            AddParameterValueAsString(it, parameters, pFamMgr == nullptr? nullptr : pFamMgr->GetFamilyParameterDefinition(it->GetParameterDefinitionUid()), false);
        }
    }
    m_parametersArrayDict.emplace_back(L"IFamilyType", TransferOwnership(parameters));
}

void Sample::ElementWrapper::CreateParam_IInstance_IFamily_IFamilyType(const IFamilyType* pFamType)
{
    CreateParam_IInstance_IFamilyManager_IFamilyType(pFamType, nullptr);
}
void Sample::ElementWrapper::CreateParam_IInstance_IFamily_InstanceType(const gcmp::IInstanceType* pInstanceType)
{
    ParametersArray parameters;
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetElementId", pInstanceType->GetElementId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetFamilyId", pInstanceType->GetFamilyId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTopFamilyId", pInstanceType->GetTopFamilyId());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsGlobal", pInstanceType->IsGlobal());
        parameters.push_back(TransferOwnership(opField));
    }
    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSub", pInstanceType->IsSub());
        parameters.push_back(TransferOwnership(opField));
    }
    m_parametersArrayDict.emplace_back(L"IInstanceType", TransferOwnership(parameters));
}

gcmp::IElement* Sample::ElementWrapper::GetElement()
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (nullptr == pDoc)
    {
        return nullptr;
    }
    IElement* pElem = pDoc->GetElement(m_elementId);
    return pElem;
}

gcmp::IInstance * Sample::ElementWrapper::GetInstance()
{
    IElement* pElem = GetElement();
    return quick_cast<gcmp::IInstance>(pElem);
}

Sample::ElementWrapper::ElementWrapper(ElementId elemId)
    : ObjectWrapper(L"Element_Probe"), m_elementId(elemId)
{
}

