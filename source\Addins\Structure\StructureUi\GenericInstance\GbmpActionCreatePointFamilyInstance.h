﻿#pragma once
#include "GcmpActionBase.h"
#include "Vector3d.h"
#include "ContainerUtil.h"
#include "IPropertyPanelEventHandler.h"

enum EnWayToSetupRelatedPlane
{
    EWS_On_Face = 0,
    EWS_On_Workplane,
};

namespace gcmp
{
    class ISnap;
    class IPickFilter;
    class IGraphicsPlane;

    class ActionCreatePointFamilyInstance : public gcmp::GcmpActionBase, public gcmp::IPropertyPanelEventHandler
    {
    public:
        ActionCreatePointFamilyInstance(EnWayToSetupRelatedPlane eWayToSetup, const gcmp::ElementId& familyId, const std::wstring& familyType);
        virtual ~ActionCreatePointFamilyInstance();

        // 继承自IAction的虚函数
    public:
        virtual void InitAction(gcmp::IUiView* pCurrentView) override;
        virtual void OnChildActionFinished(gcmp::IUiView* pCurrentView, const gcmp::ActionOutput& childActionReturnParam) override;

    public:
        void PreviewOnWorkPlane(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos, const gcmp::Vector3d& mousePt);
        void PreviewOnFace(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& mousePt);

    public:
        // 通过 IPropertyPanelEventHandler 继承
        void On(IPropertyPanelEventArgs* pArgs) override;

    private:
        void SelectPosition(gcmp::IUiView* pCurrentView);
        void SelectPositionOnWorkPlane();
        void SelectPositionOnFace(gcmp::IDocument* pDoc);
        void SetWorkPlaneForInstance(gcmp::IDocument* pDoc, const gcmp::ElementId& idPlane);
        void PreviewInstanceMoveOnPlane(gcmp::IDocument* pDoc, const gcmp::Vector3d& targetPt, gcmp::IGraphicsPlane* pPlane, gcmp::IModelView* pModelView);
        void MoveInstanceOnPlane(gcmp::IDocument* pDoc, const gcmp::Vector3d& targetPt, gcmp::IGraphicsPlane* pPlane, IUiView* pCurrentView);

        bool PickPointActionKeyDown(int nChar, gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pickedPt, const gcmp::Vector3d& mousePt);
        void OnNeedRotatePosition(gcmp::IUiView* pCurrentView);
        double GetAlignmentAngle();
    private:
        bool m_isUserCancelled;
        gcmp::Vector3d m_placePt;
        EnWayToSetupRelatedPlane m_eWayToSetup;
        gcmp::GraphicsNodeReferenceOwnerPtrVector m_pickedNodes;
        int m_nTransactionGroupId;
        gcmp::ElementId m_idNewInstance;

        //存储族的Id和族类型的名字
        gcmp::ElementId m_familyId;
        std::wstring m_familyType;

        // 已经Picked到的点集, 按照Pick顺序存储
        std::vector<gcmp::Vector3d> m_pickedPoints;

        void UpdatePreviewGRep(gcmp::IModelView* pModelView);
    private:
        bool m_bIsRotated;
        int m_rotateRightAngleCounts;
        gcmp::OwnerPtr<gcmp::ISnap> m_opSnap;
        bool m_isLastSnapped;
        double m_elementDirectionAngle;
        bool m_needAlignment;

        gcmp::OwnerPtr<gcmp::IPickFilter> m_opLocalPickFilter;
        Matrix4d m_previewMatrix;
    };
}
