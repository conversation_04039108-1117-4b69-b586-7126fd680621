﻿#include "SampleExternalObjectRegenComponent.h"
#include "IDocument.h"
#include "DbObjectUtils.h"
#include "IGenericElement.h"
#include "IElementModelShape.h"
#include "ICalculatorCollection.h"
#include "ElementUtils.h"

#include "SampleExternalObjectCalculator.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

DBOBJECT_DATA_DEFINE(SampleExternalObjectRegenComponent)
{
    SetOwnerElement(nullptr);
}

bool SampleExternalObjectRegenComponent::SetOwnerElement(gcmp::IElement* pOwnerElement)
{
    m_pOwnerElement = pOwnerElement;
    return true;
}

DECLARE_CALCULATOR_CREATOR(SampleExternalObjectGRepCalculator)
void SampleExternalObjectRegenComponent::GetCalculators(gcmp::ICalculatorCollection* calculators) const
{
    const IElement* pElement = GetOwnerElement();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pElement, L"pElement获取失败",L"GDMPLab",L"2024-12-30");
    const IElementModelShape* pElementModelShape = pElement->GetElementModelShape();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pElementModelShape, L"ElementModelShape获取失败",L"GDMPLab",L"2024-12-30");
    ADD_CALCULATOR(SampleExternalObjectGRepCalculator, pElement->GetDocument(),pElementModelShape->GetGraphicsElementShapeRdId());
    ElementUtils::GetCalculators(pElement, calculators);
}

