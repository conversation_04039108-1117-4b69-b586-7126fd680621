#include "SampleBuiltInParameterDefinitions.h"
#include "UniIdentity.h"
#include "ParameterType.h"
#include "UnitUniIdentities.h"
#include "IParameterDefinitionLibrary.h"
#include "IParameterDefinition.h"

#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;

DECLARE_GUID(SampleGeneralLengthParameter) = { 0x2ea6681f, 0xe297, 0x4153,{ 0xb2, 0xda, 0x13, 0xe5, 0xc6, 0xbd, 0x4f, 0xbc } };
const IParameterDefinition* GetParamDef_SampleGeneralLengthParameter() {
    static UniIdentity c_uid_SampleGeneralLengthParameter(c_guid_SampleGeneralLengthParameter, L"SampleGeneralLengthParameter" L"L\"长度\"" L"L\"长度\""); static OwnerPtr<IParameterDefinition> paramDefSampleGeneralLengthParameter = IParameterDefinition::CreateParameterDefinition(c_uid_SampleGeneralLengthParameter, gcmp::TranslatorManager::Get()->Translate(TR_MODULE_NAME, L"长度"), gcmp::TranslatorManager::Get()->Translate(TR_MODULE_NAME, L"几何信息"), gcmp::TranslatorManager::Get()->Translate(TR_MODULE_NAME, L"长度"), GetParameterTypeId_Length() == GetParameterTypeId_Angle() ? gcmp::ParameterStorageType::Double : GetParameterTypeId_Length() == GetParameterTypeId_Area() ? gcmp::ParameterStorageType::Double : GetParameterTypeId_Length() == GetParameterTypeId_Color() ? gcmp::ParameterStorageType::Color : GetParameterTypeId_Length() == GetParameterTypeId_Density() ? gcmp::ParameterStorageType::Double : GetParameterTypeId_Length() == GetParameterTypeId_Float() ? gcmp::ParameterStorageType::Double : GetParameterTypeId_Length() == GetParameterTypeId_Length() ? gcmp::ParameterStorageType::Double : GetParameterTypeId_Length() == GetParameterTypeId_Material() ? gcmp::ParameterStorageType::ElementId : GetParameterTypeId_Length() == GetParameterTypeId_None() ? gcmp::ParameterStorageType::Guid : GetParameterTypeId_Length() == GetParameterTypeId_Number() ? gcmp::ParameterStorageType::Int : GetParameterTypeId_Length() == GetParameterTypeId_Slope() ? gcmp::ParameterStorageType::Double : GetParameterTypeId_Length() == GetParameterTypeId_Text() ? gcmp::ParameterStorageType::String : GetParameterTypeId_Length() == GetParameterTypeId_TextStyle() ? gcmp::ParameterStorageType::Custom : GetParameterTypeId_Length() == GetParameterTypeId_Volume() ? gcmp::ParameterStorageType::Double : GetParameterTypeId_Length() == GetParameterTypeId_YesNo() ? gcmp::ParameterStorageType::Bool : gcmp::ParameterStorageType::Custom, GetParameterTypeId_Length() == GetParameterTypeId_Angle() ? GetUnitUid_Angle() : GetParameterTypeId_Length() == GetParameterTypeId_Area() ? GetUnitUid_Area() : GetParameterTypeId_Length() == GetParameterTypeId_Color() ? GetUnitUid_None() : GetParameterTypeId_Length() == GetParameterTypeId_Density() ? GetUnitUid_Density() : GetParameterTypeId_Length() == GetParameterTypeId_Length() ? GetUnitUid_Length() : GetParameterTypeId_Length() == GetParameterTypeId_Material() ? GetUnitUid_None() : GetParameterTypeId_Length() == GetParameterTypeId_None() ? GetUnitUid_None() : GetParameterTypeId_Length() == GetParameterTypeId_Number() ? GetUnitUid_Number() : GetParameterTypeId_Length() == GetParameterTypeId_Slope() ? GetUnitUid_Angle() : GetParameterTypeId_Length() == GetParameterTypeId_Text() ? GetUnitUid_None() : GetParameterTypeId_Length() == GetParameterTypeId_TextStyle() ? GetUnitUid_None() : GetParameterTypeId_Length() == GetParameterTypeId_Volume() ? GetUnitUid_Volume() : GetParameterTypeId_Length() == GetParameterTypeId_YesNo() ? GetUnitUid_None() : GetUnitUid_None(), GetParameterTypeId_Length()); if (!(paramDefSampleGeneralLengthParameter.get())) {
        if (const gcmp::DebugMessageMgr* dbgMgr = gcmp::DebugMessageMgr::GetDebugMessageMgr()) {
            dbgMgr->ShowDebugWarn("E:\\dev\\gdmplab\\source\\Addins\\CppSamples\\ElemModule\\SampleBuiltInParameterDefinitions.cpp", 13, __FUNCTION__, L"IParameterDefinition为空", L"周阳", L"2019-6-21");
        }
    }; return paramDefSampleGeneralLengthParameter.get();
} static BuiltInParameterDefinitionInitializer initializer_SampleGeneralLengthParameter(GetParamDef_SampleGeneralLengthParameter());;

DECLARE_GUID(SampleInstanceBelongToViewParameter) = { 0xca331d10, 0x3e20, 0x4345,{ 0xaa, 0xbf, 0x50, 0x9e, 0x65, 0x42, 0xa0, 0xf0 } };
IMPLEMENT_PD_AUTO(SampleInstanceBelongToViewParameter, L"属于视图", L"视图依赖", L"勾选设置为属于当前视图", PARAMETER_TYPE(YesNo));

// 参数示例
DECLARE_GUID(ParameterSample_Description) = { 0x1d1eccc6, 0x2c22, 0x4fd4, { 0xab, 0x36, 0xf5, 0x56, 0x4f, 0xea, 0x18, 0x20 } };
IMPLEMENT_PD_AUTO(ParameterSample_Description, L"备注", L"参数重载", L"备注", PARAMETER_TYPE(Text));

DECLARE_GUID(ParameterSample_ChangeParameterVisible) = { 0x8a0c751, 0x54, 0x4675, { 0x95, 0x75, 0x3f, 0x6, 0xfa, 0x17, 0xdd, 0xb9 } };
IMPLEMENT_PD_AUTO(ParameterSample_ChangeParameterVisible, L"参数可见性开关", L"参数重载", L"控制示例参数组的参数是否可见", PARAMETER_TYPE(YesNo));

DECLARE_GUID(ParameterSample_ChangeParameterModifiable) = { 0x6f0cd132, 0x15da, 0x4ddf, { 0xb3, 0x7c, 0x68, 0x8d, 0x2, 0x5b, 0xbe, 0xab } };
IMPLEMENT_PD_AUTO(ParameterSample_ChangeParameterModifiable, L"参数可编辑开关", L"参数重载", L"控制示例参数组的参数是否可编辑", PARAMETER_TYPE(YesNo));

DECLARE_GUID(ParameterSample_ChangeParameterChangGroup) = { 0x999c71cc, 0x8727, 0x4844, { 0xb6, 0x29, 0x68, 0x69, 0x69, 0x2a, 0x20, 0x0 } };
IMPLEMENT_PD_AUTO(ParameterSample_ChangeParameterChangGroup, L"改变参数分组", L"参数重载", L"改变参数分组方式", PARAMETER_TYPE(YesNo));

DECLARE_GUID(ParameterSample_ChangeParameterName) = { 0x67b541c8, 0x3433, 0x471f, { 0x83, 0xd0, 0xd3, 0x76, 0x94, 0x5c, 0xb4, 0x7c } };
IMPLEMENT_PD_AUTO(ParameterSample_ChangeParameterName, L"改变参数名称", L"参数重载", L"改变参数名称", PARAMETER_TYPE(YesNo));

DECLARE_GUID(ParameterSample_ChangeParameterFilter2d) = { 0x5281e776, 0xda0b, 0x41ba, { 0xbb, 0x92, 0xa8, 0x38, 0x33, 0xca, 0xc5, 0x8a } };
IMPLEMENT_PD_AUTO(ParameterSample_ChangeParameterFilter2d, L"视图列表仅二维", L"参数重载", L"示例参数列表只显示二维视图", PARAMETER_TYPE(YesNo));

DECLARE_GUID(ParameterSample_ColorIndex) = { 0x5592be1e, 0xa7a4, 0x46f1, { 0xb1, 0x63, 0x70, 0xa1, 0xc8, 0x49, 0x5f, 0xc } };
IMPLEMENT_PD_AUTO(ParameterSample_ColorIndex, L"颜色索引示例", L"示例颜色参数", L"颜色索引参数", PARAMETER_TYPE(Number));

DECLARE_GUID(ParameterSample_ColorModeList) = { 0xb221c1e, 0xd709, 0x4f80, { 0xb2, 0x81, 0x10, 0x8a, 0xbb, 0x9a, 0x48, 0xc8 } };
IMPLEMENT_PD_AUTO(ParameterSample_ColorModeList, L"颜色列表示例", L"示例颜色参数", L"列表参数", PARAMETER_TYPE(ValueList));

DECLARE_GUID(ParameterSample_ElementId) = { 0x2ab29c30, 0x9362, 0x4575, { 0x8c, 0xc, 0xe0, 0x36, 0xd1, 0x7e, 0x3d, 0xfc } };
IMPLEMENT_PD_AUTO(ParameterSample_ElementId, L"ElementId参数示例", L"示例参数", L"ElementId参数", PARAMETER_TYPE(None));

DECLARE_GUID(ParameterSample_Length) = { 0x5a91013f, 0x8abe, 0x4f4d, { 0xb7, 0x8b, 0xa1, 0xa8, 0x6b, 0x51, 0xa9, 0x85 } };
IMPLEMENT_PD_AUTO(ParameterSample_Length, L"长度示例", L"示例参数", L"长度参数", PARAMETER_TYPE(Length));

DECLARE_GUID(ParameterSample_LengthMax) = { 0x55c03dff, 0xd6ea, 0x4286, { 0x83, 0x1d, 0x93, 0x7b, 0x1a, 0x2f, 0xf, 0xb4 } };
IMPLEMENT_PD_AUTO(ParameterSample_LengthMax, L"最大长度示例", L"示例参数", L"最大长度参数", PARAMETER_TYPE(Length));

DECLARE_GUID(ParameterSample_Width) = { 0x56413263, 0x7365, 0x4694, { 0x88, 0x75, 0x4d, 0x5f, 0x9f, 0x73, 0x43, 0x15 } };
IMPLEMENT_PD_AUTO(ParameterSample_Width, L"宽度示例", L"示例参数", L"宽度参数", PARAMETER_TYPE(Length));

DECLARE_GUID(ParameterSample_Height) = { 0x71992792, 0x4373, 0x4373, { 0x91, 0x63, 0x79, 0xd2, 0x77, 0x83, 0x43, 0x71 } };
IMPLEMENT_PD_AUTO(ParameterSample_Height, L"高度示例", L"示例参数", L"高度参数", PARAMETER_TYPE(Length));

DECLARE_GUID(ParameterSample_Volume) = { 0x74251864, 0x8189, 0x4819, { 0xb7, 0x3d, 0x94, 0xd6, 0x7d, 0x96, 0x47, 0x72 } };
IMPLEMENT_PD_AUTO(ParameterSample_Volume, L"体积示例", L"示例参数", L"体积参数", PARAMETER_TYPE(Volume));

DECLARE_GUID(ParameterSample_Color) = { 0x23456789, 0x1234, 0x5678, { 0x90, 0x12, 0x34, 0x56, 0x78, 0x90, 0x12, 0x34 } };
IMPLEMENT_PD_AUTO(ParameterSample_Color, L"颜色示例", L"示例参数", L"颜色参数", PARAMETER_TYPE(Color));

DECLARE_GUID(ParameterSample_Material) = { 0x12345678, 0x9012, 0x3456, { 0x78, 0x90, 0x12, 0x34, 0x56, 0x78, 0x90, 0x12 } };
IMPLEMENT_PD_AUTO(ParameterSample_Material, L"材质示例", L"示例参数", L"材质参数", PARAMETER_TYPE(Material));

DECLARE_GUID(ParameterSample_ViewList) = { 0x6cb639a9, 0x31d2, 0x4e95, { 0x9e, 0xea, 0x6, 0x53, 0xf4, 0x7a, 0x5d, 0xf0 } };
IMPLEMENT_PD(ParameterSample_ViewList, GBMP_TR(L"视图列表示例"), GBMP_TR(L"示例参数"), GBMP_TR(L"视图列表参数"), L"PD - Views", ParameterStorageType::ElementId, UNIT(None), PARAMETER_TYPE(ValueList));

DECLARE_GUID(ParameterSample_ViewName) = { 0xa23ca6b0, 0x28bc, 0x4849, { 0x9e, 0xd4, 0xf3, 0xe2, 0x24, 0xf2, 0x72, 0x50 } };
IMPLEMENT_PD_AUTO(ParameterSample_ViewName, L"视图名称", L"示例参数", L"视图名称", PARAMETER_TYPE(Text));