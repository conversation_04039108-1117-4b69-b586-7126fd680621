#pragma once

#include "ElemModule.h"
#include "IParameterDefinition.h"
#include "ParameterType.h"
#include "UnitUniIdentities.h"
#include "TranslatorManager.h"

/// \brief 简化的参数定义实现宏，根据参数类型自动推断存储类型和单位类型
///
/// 该宏根据常见的参数类型使用模式，自动匹配存储类型和单位类型，简化了参数定义的实现过程
/// \param InternalName 内建参数枚举变量名
/// \param Name 内建参数名称
/// \param GroupName 内建参数分组名称
/// \param Description 内建参数描述信息
/// \param ParameterType 内建参数类型（如Length, Area, Angle等）
#define IMPLEMENT_PD_AUTO(InternalName, Name, GroupName, Description, ParameterType) \
    IMPLEMENT_PD(InternalName, GBMP_TR(Name), GBMP_TR(GroupName), GBMP_TR(Description), L#InternalName, \
        ParameterType == PARAMETER_TYPE(Angle) ? gcmp::ParameterStorageType::Double : \
        ParameterType == PARAMETER_TYPE(Area) ? gcmp::ParameterStorageType::Double : \
        ParameterType == PARAMETER_TYPE(Color) ? gcmp::ParameterStorageType::Color : \
        ParameterType == PARAMETER_TYPE(Density) ? gcmp::ParameterStorageType::Double : \
        ParameterType == PARAMETER_TYPE(Float) ? gcmp::ParameterStorageType::Double : \
        ParameterType == PARAMETER_TYPE(Length) ? gcmp::ParameterStorageType::Double : \
        ParameterType == PARAMETER_TYPE(Material) ? gcmp::ParameterStorageType::ElementId : \
        ParameterType == PARAMETER_TYPE(None) ? gcmp::ParameterStorageType::Guid : \
        ParameterType == PARAMETER_TYPE(Number) ? gcmp::ParameterStorageType::Int : \
        ParameterType == PARAMETER_TYPE(Slope) ? gcmp::ParameterStorageType::Double : \
        ParameterType == PARAMETER_TYPE(Text) ? gcmp::ParameterStorageType::String : \
        ParameterType == PARAMETER_TYPE(TextStyle) ? gcmp::ParameterStorageType::Custom : \
        ParameterType == PARAMETER_TYPE(Volume) ? gcmp::ParameterStorageType::Double : \
        ParameterType == PARAMETER_TYPE(YesNo) ? gcmp::ParameterStorageType::Bool : \
        gcmp::ParameterStorageType::Custom, \
        ParameterType == PARAMETER_TYPE(Angle) ? UNIT(Angle) : \
        ParameterType == PARAMETER_TYPE(Area) ? UNIT(Area) : \
        ParameterType == PARAMETER_TYPE(Color) ? UNIT(None) : \
        ParameterType == PARAMETER_TYPE(Density) ? UNIT(Density) : \
        ParameterType == PARAMETER_TYPE(Length) ? UNIT(Length) : \
        ParameterType == PARAMETER_TYPE(Material) ? UNIT(None) : \
        ParameterType == PARAMETER_TYPE(None) ? UNIT(None) : \
        ParameterType == PARAMETER_TYPE(Number) ? UNIT(Number) : \
        ParameterType == PARAMETER_TYPE(Slope) ? UNIT(Angle) : \
        ParameterType == PARAMETER_TYPE(Text) ? UNIT(None) : \
        ParameterType == PARAMETER_TYPE(TextStyle) ? UNIT(None) : \
        ParameterType == PARAMETER_TYPE(Volume) ? UNIT(Volume) : \
        ParameterType == PARAMETER_TYPE(YesNo) ? UNIT(None) : \
        UNIT(None), \
        ParameterType)

#define DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(internalName) \
    ELEMMODULE_EXPORT const gcmp::IParameterDefinition* GetParamDef_##internalName();

DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(SampleGeneralLengthParameter);                     // 长度
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(SampleInstanceBelongToViewParameter);               // 示例IInstance使用自定义图形表达

// 参数示例
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ChangeParameterVisible);       // 布尔参数控制其他参数是否可见
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ChangeParameterModifiable);    // 布尔参数控制其他参数是否可编辑
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ChangeParameterFilter2d);      // 布尔参数控制其他参数是否可编辑
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ChangeParameterChangGroup);     // 布尔参数改变参数分组
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ChangeParameterName);           // 布尔参数改变参数名称

DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_Description);                  // 纯文字参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ColorIndex);                   // 颜色索引参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ColorModeList);                // 列表参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ElementId);                    // ElementId参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_Length);                       // 长度参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_LengthMax);                    // 最大长度参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_Width);                        // 宽度参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_Height);                       // 高度参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_Volume);                       // 体积参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_Color);                        // 颜色参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_Material);                     // 材质参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ViewList);                     // 视图列表参数
DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION(ParameterSample_ViewName);                     // 视图名称参数

#undef DEFINE_SAMPLE_BUILT_IN_PARAMETER_DEFINITION // 仅限该头文件中使用