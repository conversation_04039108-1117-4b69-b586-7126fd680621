#pragma once

#include "CommandBase.h"

using namespace gcmp;

namespace Sample
{
    class CmdExternalParameter : public CommandBase
    {
    public:
        CmdExternalParameter(void);
        ~CmdExternalParameter(void);

        // Command接口实现
    public:
        gcmp::OwnerPtr<gcmp::IAction> ExecuteCommand(const gcmp::CommandParameters& cmdParams) override;

        virtual bool IsVisible() const override { return true; }
        virtual bool IsEnabled() const override { return true; }
        virtual bool ShouldPopExistingActionsBeforeExecution() const override { return false; }

    private:
        // 示例1：创建项目参数并绑定
        bool CreateProjectParameter();
        // 示例2：创建和管理共享参数
        bool CreateSharedParameter();
        // 示例3：加载和保存外部参数
        bool LoadAndSaveExternalParameters();
        // 示例4：绑定外部参数到模型元素
        bool LoadAndBindExternalParameters();
        // 示例5：绑定外部参数到模型元素
        bool ExternalParameterSample();
    };
}