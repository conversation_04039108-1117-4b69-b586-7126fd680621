﻿#include "SampleExternalObjectParamCustomizer.h"
#include "IParameter.h"
#include "NdbUpgrade.h"
#include "IElementStatus.h"
#include "IGenericElement.h"
#include "ICurve3d.h"
#include "IGraphicsCurve3d.h"
#include "DbObjectUtils.h"
#include "ParameterStorageType.h"
#include "IElementBasicInformation.h"
#include "IParameterDefinitionLibrary.h"
#include "IElementParameters.h"

#include "ParamSampleExternalObject.h"
#include "SampleBuiltInParameterDefinitions.h"
#include "IRegenerator.h"
#include "IParameterValueStorage.h"
#include "IParameterValidator.h"
#include "IElementParameterBindings.h"
#include "GcmpBuiltInParameterDefinitions.h"
#include "IModelView.h"
#include "IParameterValueElementId.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

///////////////////////////////////////////////////////////////////////////////////////////////////////
DBOBJECT_DATA_DEFINE(SampleExternalObjectParamCustomizer)
{
    SetOwnerElement(nullptr);
}

void SampleExternalObjectParamCustomizer::ReportParameterDefinitions(std::vector<int>* pParamDefIds) const
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(pParamDefIds, L"pParamDefIds为空", L"GDMPLab", L"2024-03-30");
    pParamDefIds->push_back(PARAMETER_ID(ParameterSample_Length));
    pParamDefIds->push_back(PARAMETER_ID(ParameterSample_LengthMax));
    pParamDefIds->push_back(PARAMETER_ID(ParameterSample_Width));
    pParamDefIds->push_back(PARAMETER_ID(ParameterSample_Volume));
    pParamDefIds->push_back(PARAMETER_ID(ParameterSample_ColorModeList));
    pParamDefIds->push_back(PARAMETER_ID(ParameterSample_Material));
    pParamDefIds->push_back(PARAMETER_ID(ParameterSample_ViewList));
    
    const IGenericElement* pGenericElement = dynamic_cast<const IGenericElement*>(GetOwnerElement());
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGenericElement, L"pGenericElement为空", L"GDMPLab", L"2024-12-30");
    const ParamSampleExternalObject* pSampleObject = dynamic_cast<const ParamSampleExternalObject*>(pGenericElement->GetExternalObject());
    DBG_WARN_AND_RETURN_VOID_UNLESS(pSampleObject, L"pSampleObject为空", L"GDMPLab", L"2024-12-30");
    if (pSampleObject->GetColorMode() == ColorMode::ByColor)
    {
        pParamDefIds->push_back(PARAMETER_ID(ParameterSample_Color));
    }
    else if (pSampleObject->GetColorMode() == ColorMode::ByColorIndex)
    {
        pParamDefIds->push_back(PARAMETER_ID(ParameterSample_ColorIndex));
    }
}

OwnerPtr<IParameter> SampleExternalObjectParamCustomizer::GetNativeParameter(int paramDefId) const
{
    const IGenericElement* pGenericElement = quick_cast<IGenericElement>(GetOwnerElement());
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pGenericElement, L"pGenericElement为空",L"GDMPLab",L"2024-12-30");
    const ParamSampleExternalObject* pSampleObject = quick_cast<ParamSampleExternalObject>(pGenericElement->GetExternalObject());
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pSampleObject,L"pSampleObject为空",L"GDMPLab",L"2024-12-30");

    OwnerPtr<IParameter> opParam;
    if (paramDefId == PARAMETER_ID(ParameterSample_Length))
    {
        ParameterAttributes pa = { false, false, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::Double, pa,
            PARAMETER_UID(ParameterSample_Length), ParameterProcessType::GeneralInput);
        opParam->SetValueAsDouble(pSampleObject->GetLength());

        IParameterValueStorage* pParamValueStorage = opParam->GetParameterValueStorageFw();
        OwnerPtr<IParameterValidator> opParamValidator = IParameterValidator::CreateDoubleRangeValidator(Constants::MIN_MODEL_SIZE, pSampleObject->GetLengthMax());
        pParamValueStorage->SetValidator(TransferOwnership(opParamValidator));
        return TransferOwnership(opParam);
    }
    if (paramDefId == PARAMETER_ID(ParameterSample_LengthMax))
    {
        ParameterAttributes pa = { false, false, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::Double, pa,
            PARAMETER_UID(ParameterSample_LengthMax), ParameterProcessType::GeneralInput);
        opParam->SetValueAsDouble(pSampleObject->GetLengthMax());

        IParameterValueStorage* pParamValueStorage = opParam->GetParameterValueStorageFw();
        OwnerPtr<IParameterValidator> opParamValidator = IParameterValidator::CreateDoubleRangeValidator(Constants::MIN_MODEL_SIZE, Constants::MAX_MODEL_SIZE);
        pParamValueStorage->SetValidator(TransferOwnership(opParamValidator));
        return TransferOwnership(opParam);
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_Width))
    {
        ParameterAttributes pa = { false, true, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::Double, pa,
            PARAMETER_UID(ParameterSample_Width), ParameterProcessType::GeneralInput);
        opParam->SetValueAsDouble(pSampleObject->GetWidth());
        return TransferOwnership(opParam);
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_Color))
    {
        ParameterAttributes pa = { false, false, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::Color, pa,
            PARAMETER_UID(ParameterSample_Color), ParameterProcessType::GeneralInput);
        opParam->SetValueAsColor(pSampleObject->GetColor());
        return TransferOwnership(opParam);
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_Volume))
    {
        ParameterAttributes pa = { false, false, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::Double, pa,
            PARAMETER_UID(ParameterSample_Volume), ParameterProcessType::GeneralOutput);
        opParam->SetValueAsDouble(pSampleObject->GetVolume());
        return TransferOwnership(opParam);
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_Material))
    {
        ParameterAttributes pa = { false, false, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::ElementId, pa,
            PARAMETER_UID(ParameterSample_Material), ParameterProcessType::GeneralInput);
        opParam->SetValueAsElementId(pSampleObject->GetMaterialId());
        return TransferOwnership(opParam);
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_ColorModeList))
    {
        ParameterAttributes pa = { false, false, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::Int, pa,
            PARAMETER_UID(ParameterSample_ColorModeList), ParameterProcessType::GeneralInput);
        opParam->SetValueAsInt(int(pSampleObject->GetColorMode()));
        return TransferOwnership(opParam);
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_ColorIndex))
    {
        ParameterAttributes pa = { false, false, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::Int, pa,
            PARAMETER_UID(ParameterSample_ColorIndex), ParameterProcessType::GeneralInput);
        opParam->SetValueAsInt(pSampleObject->GetColorIndex());
        return TransferOwnership(opParam);
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_ViewList))
    {
        ParameterAttributes pa = { false, true, true };
        opParam = IParameter::CreateParameter(GetDocument(), ParameterStorageType::ElementId, pa,
            PARAMETER_UID(ParameterSample_ViewList), ParameterProcessType::GeneralInput);
        opParam->SetValueAsElementId(pSampleObject->GetViewId());
        return TransferOwnership(opParam);
    }
    return nullptr;
}

bool SampleExternalObjectParamCustomizer::SetNativeParameter(const IParameter *param, std::wstring* errorMsg)
{
    DBG_WARN_AND_RETURN_FALSE_UNLESS(param, L"param is null.",L"GDMPLab",L"2024-12-30");

    if (errorMsg)
    {
        errorMsg->clear();
    }

    IGenericElement* pGenericElement = dynamic_cast<IGenericElement*>(GetOwnerElement());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGenericElement, L"pGenericElement为空",L"GDMPLab",L"2024-12-30");
    ParamSampleExternalObject* pSampleObject = dynamic_cast<ParamSampleExternalObject*>(pGenericElement->GetExternalObject());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pSampleObject, L"pSampleObject为空",L"GDMPLab",L"2024-12-30");

    IDocument* pDoc = pGenericElement->GetDocument();
    DBG_WARN_AND_RETURN_UNLESS(pDoc, false, L"pDoc为空", L"王晓磊", L"2020-08-26");
    IRegenerator* pRegenerator = pDoc->GetRegenerator();
    DBG_WARN_AND_RETURN_UNLESS(pRegenerator, false, L"pRegenerator为空", L"王晓磊", L"2020-08-26");
    IElementParameters* pElemParameters = pGenericElement->GetElementParameters();

    int paramDefId = param->GetParameterDefinitionId();

    if (paramDefId == PARAMETER_ID(ParameterSample_Length))
    {
        pSampleObject->SetLength(param->GetValueAsDouble());
        pRegenerator->MarkRegenDataId(pElemParameters->GetParameterRdId(PARAMETER_ID(ParameterSample_Length)));
        return true;
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_LengthMax))
    {
        double maxLength = param->GetValueAsDouble();
        pSampleObject->SetLengthMax(maxLength);
        if (maxLength < pSampleObject->GetLength())
        {
            pSampleObject->SetLength(maxLength);
            pRegenerator->MarkRegenDataId(pElemParameters->GetParameterRdId(PARAMETER_ID(ParameterSample_Length)));
        }
        return true;
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_Width))
    {
        pSampleObject->SetWidth(param->GetValueAsDouble());
        pRegenerator->MarkRegenDataId(pElemParameters->GetDriveParameterRdId());
        return true;
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_Color))
    {
        pSampleObject->SetColor(param->GetValueAsColor());
        return true;
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_Material))
    {
        pSampleObject->SetMaterialId(param->GetValueAsElementId());
        pRegenerator->MarkRegenDataId(pElemParameters->GetParameterRdId(PARAMETER_ID(ParameterSample_Material)));
        return true;
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_ColorModeList))
    {
        pSampleObject->SetColorMode((ColorMode)param->GetValueAsInt());
        return true;
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_ColorIndex))
    {
        pSampleObject->SetColorIndex(param->GetValueAsInt());
        return true;
    }
    else if (paramDefId == PARAMETER_ID(ParameterSample_ViewList))
    {
        const IParameterValueStorage* pParamValueStorage = param->GetParameterValueStorage();
        ElementId viewId = ElementId::InvalidID;
        if (pParamValueStorage->GetParameterStorageType() == ParameterStorageType::ElementId)
        {
            viewId = param->GetValueAsElementId();
            const IModelView* pView = quick_cast<IModelView>(pDoc->GetElement(viewId));
            if (pView)
            {
                pSampleObject->SetViewTempText(L"");
            }
            else
            {
                pSampleObject->SetViewTempText(pView->GetName());
                viewId = ElementId::InvalidID;
            }
            pSampleObject->SetViewId(viewId);
        }
        else
        {
            pSampleObject->SetViewId(viewId);
            pSampleObject->SetViewTempText(L"");
        }

        IModelView* pModelView = quick_cast<IModelView>(pDoc->GetElement(viewId));
        if (pModelView)
        { 
            OwnerPtr<IParameter> opViewNameParam = pElemParameters->GetParameterById(PARAMETER_ID(ParameterSample_ViewName));
            opViewNameParam->SetValueAsString(pModelView->GetName());
            bool hasParameter = pElemParameters->SetParameter(opViewNameParam.get());
        }

        // 因为IElementParameterBindings所绑定的参数必须是驱动参数，下面代码不工作。暂时保留，待支持非驱动参数时恢复。
        //IElementParameterBindings* pParameterBindings = pGenericElement->GetElementParameters()->GetElementParameterBindings();
        //const IParameterBinding* pParamBinding = pParameterBindings->GetParameterBinding(PARAMETER_ID(ParameterSample_ViewName));
        //int test = PARAMETER_ID(ElementNameBuiltInParameter);
        //if(viewId == ElementId::InvalidID)
        //{ 
        //    if (pParamBinding)
        //    {
        //        pParameterBindings->DeleteParameterBinding(PARAMETER_ID(ParameterSample_ViewName));
        //    }
        //}
        //else
        //{
        //    if (pParamBinding)
        //    {
        //        pParameterBindings->ChangeParameterBinding(PARAMETER_ID(ParameterSample_ViewName), PARAMETER_ID(ParameterSample_Height), GetOwnerElement()->GetElementId());
        //    }
        //    else
        //    {
        //        bool flag = pParameterBindings->AddParameterBinding(PARAMETER_ID(ParameterSample_ViewName), PARAMETER_ID(ParameterSample_Height), GetOwnerElement()->GetElementId(), false);
        //        DBG_WARN_UNLESS(flag, L"AddParameterBinding失败", L"GDMPLab", L"2024-12-30");
        //    }
        //}
        pRegenerator->MarkRegenDataId(pElemParameters->GetParameterRdId(PARAMETER_ID(ParameterSample_ViewList)));
        return true;
    }
    
    return true;
}

bool SampleExternalObjectParamCustomizer::IsParameterModifiable(int parameterId) const
{
    const IGenericElement* pGenericElement = dynamic_cast<const IGenericElement*>(GetOwnerElement());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGenericElement, L"pGenericElement为空",L"GDMPLab",L"2024-12-30");
    const ParamSampleExternalObject* pSampleObject = dynamic_cast<const ParamSampleExternalObject*>(pGenericElement->GetExternalObject());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pSampleObject, L"pSampleObject为空",L"GDMPLab",L"2024-12-30");

    const IElementStatus* pStatus = pGenericElement->GetStatus();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pStatus, L"无法获取状态集",L"GDMPLab",L"2024-12-30");
    return pStatus->IsModifiable();
}

void Sample::SampleExternalObjectParamCustomizer::FilterParameterValues(const UniIdentity& paramDefUid, std::vector<OwnerPtr<IParameterValueStorage>>& values) const
{
    const IGenericElement* pGenericElement = dynamic_cast<const IGenericElement*>(GetOwnerElement());
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGenericElement, L"pGenericElement为空", L"GDMPLab", L"2024-12-30");
    const ParamSampleExternalObject* pSampleObject = dynamic_cast<const ParamSampleExternalObject*>(pGenericElement->GetExternalObject());
    DBG_WARN_AND_RETURN_VOID_UNLESS(pSampleObject, L"pSampleObject为空", L"GDMPLab", L"2024-12-30");

    const IElementParameters* pElementParameters = pGenericElement->GetElementParameters();
    bool needFilter2d = false;
    pElementParameters->GetParameterValueAsBool(PARAMETER_ID(ParameterSample_ChangeParameterFilter2d), needFilter2d);

    if (needFilter2d && paramDefUid == PARAMETER_UID(ParameterSample_ViewList))
    {
        values.erase(
            std::remove_if(values.begin(), values.end(),
                [this](const OwnerPtr<IParameterValueStorage>& pValue)
                {
                    if (const IParameterValueElementId* pParamElemId = quick_cast<IParameterValueElementId>(pValue.get()))
                    {
                        ElementId viewId = pParamElemId->GetValue();
                        const IModelView* pView = quick_cast<IModelView>(GetDocument()->GetElement(viewId));
                        if (pView && pView->GetViewType() != BuiltInViewType::TwoDimensional)
                        {
                            return true; // 标记需要移除
                        }
                    }
                    return false; // 保留元素
                }),
            values.end()
        );
    }
}

IDocument * SampleExternalObjectParamCustomizer::GetDocument() const
{
    if (!m_pOwnerElement)
    {
        return nullptr;
    }
    return m_pOwnerElement->GetDocument();
}

ElementId SampleExternalObjectParamCustomizer::GetOwnerElementId() const
{
    if (!m_pOwnerElement)
    {
        return ElementId::InvalidID;
    }
    return m_pOwnerElement->GetElementId();
}

