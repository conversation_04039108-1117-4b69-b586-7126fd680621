﻿#include "ParamSampleExternalObject.h"
#include "Box2d.h"
#include "Box3d.h"
#include "ILine3d.h"
#include "IDocument.h"
#include "ElementUtils.h"
#include "DbObjectUtils.h"
#include "NdbDataSchema.h"
#include "ILineTypeData.h"
#include "IGenericElement.h"
#include "IElementBasicInformation.h"
#include "GcmpBuiltInCategoryUniIdentities.h"
#include "SampleExternalObjectRegenComponent.h"
#include "IElementParameters.h"
#include "SampleExternalObjectParamCustomizer.h"
#include "IBody.h"
#include "GmBodyBuilder.h"
#include "IGraphicsBRepBody.h"
#include "IGraphicsElementShape.h"
#include "IElementModelShape.h"
#include "IParameterDefinitionLibrary.h"
#include "SampleBuiltInParameterDefinitions.h"
#include "IElementPosition.h"
#include "IElementTransformationComponent.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"
#include "IGraphicsStyle.h"
#include "IGraphicsStyleData.h"
#include "IGraphicsMaterial.h"
#include "IGraphicsRichText.h"
#include "IModelView.h"
#include "IRegenerator.h"
#include "SampleParameterGroupOverride.h"
#include "SampleParameterOverride.h"
#include "IGraphicsMaterialManager.h"

using namespace gcmp;
using namespace Sample;

DBOBJECT_DATA_DEFINE(ParamSampleExternalObject)
{
    m_pOwnerElement = nullptr;
    m_pOwnerDocument = nullptr;

    m_ListValueIndex = 0;
    m_ListValueCount = 5;
    m_ColorIndex = 0;
    m_Length = 1000;
    m_LengthMax = 10000;
    m_Width = 1000;
    m_LengthMax = 10000;
    m_ViewId = ElementId::InvalidID;
    m_ColorMode = ColorMode::ByColor;
    m_Color = Color::Red;
}

bool ParamSampleExternalObject::SetOwnerElement(IElement * pOwnerElement)
{
    m_pOwnerElement = pOwnerElement;
    return true;
}

namespace
{
    bool InitParamSampleExternalObject(IElement* pElement, void* pInitData)
    {
        IDocument* pDoc = pElement->GetDocument();

        IGenericElement* pGenericElement = quick_cast<IGenericElement>(pElement);
        DBG_WARN_AND_RETURN_FALSE_UNLESS(pGenericElement, L"创建IGenericElement失败", L"GDMPLab", L"2024-12-30");

        ParamSampleExternalObject* pSampleExtObj = quick_cast<ParamSampleExternalObject>(pGenericElement->GetExternalObject());

        pGenericElement->SetElementRegenerationComponent(NEW_AS_OWNER_PTR(SampleExternalObjectRegenComponent));

        IElementParameters* pElementParameters = pGenericElement->GetElementParameters();
        DBG_WARN_AND_RETURN_FALSE_UNLESS(pElementParameters, L"获取ElementParameters失败", L"GDMPLab", L"2024-12-30");
        OwnerPtr<SampleExternalObjectParamCustomizer> opParamCustomizer = NEW_AS_OWNER_PTR(SampleExternalObjectParamCustomizer, pGenericElement);
        opParamCustomizer->SetOwnerElement(pGenericElement);
        pElementParameters->SetElementParametersCustomizer(TransferOwnership(opParamCustomizer));

        {
            ParameterAttributes pa = { false, true, true };
            OwnerPtr<IParameter> opParam = IParameter::CreateParameter(pDoc, ParameterStorageType::Double, pa,
                PARAMETER_UID(ParameterSample_Height), ParameterProcessType::GeneralInput);
            opParam->SetValueAsDouble(1000);
            pElementParameters->AddEmbeddedParameter(opParam.get(), true);
        }
        {
            ParameterAttributes pa = { false, false, true };
            OwnerPtr<IParameter> opParam = IParameter::CreateParameter(pDoc, ParameterStorageType::String, pa,
                PARAMETER_UID(ParameterSample_Description), ParameterProcessType::GeneralInput);
            opParam->SetValueAsString(L"备注");
            pElementParameters->AddEmbeddedParameter(opParam.get(), true);

            pElementParameters->AddIndividualParameterOverride(NEW_AS_OWNER_PTR(SampleParameterOverride, PARAMETER_UID(ParameterSample_Description)));
        }
        UniIdentity overrideParamUids[] = { PARAMETER_UID(ParameterSample_ChangeParameterVisible), PARAMETER_UID(ParameterSample_ChangeParameterModifiable),
        PARAMETER_UID(ParameterSample_ChangeParameterChangGroup) ,PARAMETER_UID(ParameterSample_ChangeParameterName) ,PARAMETER_UID(ParameterSample_ChangeParameterFilter2d) };
        for(UniIdentity& uid : overrideParamUids)
        {
            ParameterAttributes pa = { false, false, true };
            OwnerPtr<IParameter> opParam = IParameter::CreateParameter(pDoc, ParameterStorageType::Bool, pa, uid, ParameterProcessType::GeneralInput);
            opParam->SetValueAsBool(false);
            pElementParameters->AddEmbeddedParameter(opParam.get());
        }
        {
            ParameterAttributes pa = { false, false, true };
            OwnerPtr<IParameter> opParam = IParameter::CreateParameter(pDoc, ParameterStorageType::String, pa,
                PARAMETER_UID(ParameterSample_ViewName), ParameterProcessType::GeneralOutput);
            pElementParameters->AddEmbeddedParameter(opParam.get(), true);
        }
        pElementParameters->SetElementParameterGroupOverride(NEW_AS_OWNER_PTR(SampleParameterGroupOverride, pGenericElement));
        return true;
    }
}

ParamSampleExternalObject* Sample::ParamSampleExternalObject::Create(IDocument* pDoc)
{
    ElementInitializationOptions initOptions(InitParamSampleExternalObject, nullptr);
    OwnerPtr<ParamSampleExternalObject> opSampleExtObj = NEW_AS_OWNER_PTR(ParamSampleExternalObject);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opSampleExtObj, L"创建PolyLine2d失败", L"GDMPLab", L"2024-12-30");
    Guid clfId = ParamSampleExternalObject::GetClassId().GetGuid();
    IGenericElement* pGenericElement = IGenericElement::Create(pDoc, TransferOwnership(opSampleExtObj), clfId,
        BuiltInCategoryUniIdentities::BICU_FORM,  ComponentCreationOptions(), ElementCreationOptions::Normal, GuidUtils::GetInvalid(), &initOptions);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pGenericElement, L"创建IGenericElement失败", L"GDMPLab", L"2024-12-30");

    ParamSampleExternalObject* pSampleExtObj = quick_cast<ParamSampleExternalObject>(pGenericElement->GetExternalObject());

    IRegenerator* pRegenerator = pDoc->GetRegenerator();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pRegenerator, L"pRegenerator为空", L"王晓磊", L"2020-08-26");

    pRegenerator->ForceRegeneratingElements({ pGenericElement ->GetElementId()});

    return pSampleExtObj;
}

const ParamSampleExternalObject * ParamSampleExternalObject::Get(IDocument *pDoc, const ElementId & id)
{
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pDoc && id.IsValid(), L"参数为空",L"GDMPLab",L"2024-12-30");
    const IGenericElement *pGenericElement = quick_cast<IGenericElement>(pDoc->GetElement(id));
    if (pGenericElement && pGenericElement->GetExternalObject())
    {
        const ParamSampleExternalObject * pObject = quick_cast<ParamSampleExternalObject>(pGenericElement->GetExternalObject());
        return pObject;
    }

    return nullptr;
}

ParamSampleExternalObject * ParamSampleExternalObject::GetFw(IDocument *pDoc, const ElementId & id)
{
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pDoc && id.IsValid(), L"参数为空",L"GDMPLab",L"2024-12-30");
    IGenericElement *pGenericElement = quick_cast<IGenericElement>(pDoc->GetElement(id));
    if (pGenericElement && pGenericElement->GetExternalObject())
    {
        ParamSampleExternalObject *pObject = quick_cast<ParamSampleExternalObject>(pGenericElement->GetExternalObject());
        return pObject;
    }

    return nullptr;
}

const Guid & ParamSampleExternalObject::GetClassificationGuid()
{
    static const Guid s_categoryUidGuid = { 0x7028c9e1, 0xd491, 0x4b12,{ 0x8f, 0xaf, 0x6, 0x29, 0x37, 0xd6, 0x8d, 0xbe } };
    return s_categoryUidGuid;
}

NdbObject* ParamSampleExternalObject::GetTopOwnerObject() const
{
    return quick_cast<NdbObject>(const_cast<IElement*>(GetOwnerElement()));
}

void Sample::ParamSampleExternalObject::SetMaterialId(ElementId elementId)
{
    SetMaterialId__(elementId);
    IDocument* pDocument = GetDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDocument, L"获取Document失败", L"GDMPLab", L"2024-12-30");
    IGraphicsMaterialManager* pMaterialManager = pDocument->GetGraphicsMaterialManager();
    pMaterialManager->OverrideGraphicsMaterial({ GetOwnerElement()->GetElementId() }, elementId);
    return;
}

double Sample::ParamSampleExternalObject::GetVolume() const
{
    const IGenericElement* pGenericElement = quick_cast<IGenericElement>(GetOwnerElement());
    DBG_WARN_AND_RETURN_UNLESS(pGenericElement, 0, L"pGenericElement为空", L"GDMPLab", L"2024-12-30");
    const IElementParameters* pElemParameters = pGenericElement->GetElementParameters();
    double height;
    pElemParameters->GetParameterValueAsDouble(PARAMETER_UID(ParameterSample_Height), height);
    return GetLength() * GetWidth() * height;
}

OwnerPtr<IGraphicsElementShape> Sample::ParamSampleExternalObject::CreateGRep()
{
    OwnerPtr<IGraphicsElementShape> opGraphicsElementShape = IGraphicsElementShape::Create(GraphicsRenderLayer::Model);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opGraphicsElementShape, L"GraphicsElementShape创建失败", L"GDMPLab", L"2024-12-30");

    const IGenericElement* pGenericElement = dynamic_cast<IGenericElement*>(GetOwnerElement());
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pGenericElement, L"pGenericElement为空", L"GDMPLab", L"2024-12-30");
    const IElementParameters* pElemParameters = pGenericElement->GetElementParameters();
    double length, width, height;
    pElemParameters->GetParameterValueAsDouble(PARAMETER_UID(ParameterSample_Length), length);
    pElemParameters->GetParameterValueAsDouble(PARAMETER_UID(ParameterSample_Width), width);
    pElemParameters->GetParameterValueAsDouble(PARAMETER_UID(ParameterSample_Height), height);

    OwnerPtr<IBody> opBody = GmBodyBuilder::CreateSolidCuboid(Coordinate3d(), Vector3d(length, width, height));
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opBody, L"Body创建失败", L"GDMPLab", L"2024-12-30");
    OwnerPtr<IGraphicsBRepBody> opGraphicsBRepBody = IGraphicsBRepBody::Create(TransferOwnership(opBody));
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opGraphicsBRepBody, L"opGraphicsBRepBody创建失败", L"GDMPLab", L"2024-12-30");
    opGraphicsElementShape->AddChild(TransferOwnership(opGraphicsBRepBody));

    if (!opGraphicsElementShape->GetGraphicsStyleId().IsValid())
    {
        IGraphicsStyle* pGStyle = IGraphicsStyle::Create(GetDocument(), ElementCreationOptions::Normal);
        OwnerPtr<IGraphicsStyleData> opGStyleData = IGraphicsStyleData::Create();
        pGStyle->SetGraphicsStyleData(*opGStyleData);
        opGraphicsElementShape->SetGraphicsStyleId(pGStyle->GetElementId());
        pGStyle->SetOwnerId(pGenericElement->GetElementId());
    }

    IDocument* pDocument = pGenericElement->GetDocument();
    std::wstring viewName = L"";
    if (IModelView* pModelView = quick_cast<IModelView>(pDocument->GetElement(GetViewId())))
    {
        viewName = pModelView->GetName();
    }
    else
    {
        viewName = GetViewTempText();
    }

    if (viewName != L"")
    {
        OwnerPtr<IGraphicsRichText> opGRichText = IGraphicsRichText::Create(pDocument, viewName, Vector3d::Zero, 2000, 200, false);
        opGRichText->SetCharHeight(200, nullptr);
        opGRichText->SetVerticalAlignmentType(VerticalAlignmentType::Bottom);
        opGRichText->SetHorizontalAlignmentType(HorizontalAlignmentType::Left);
        opGRichText->SetAnchorPointPosition(Vector3d(0, 0, height + 1));
        opGraphicsElementShape->AddChild(TransferOwnership(opGRichText));
    }

    return opGraphicsElementShape;
}

void Sample::ParamSampleExternalObject::UpdateColor()
{
    IDocument* pDocument = GetDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDocument, L"获取Document失败", L"GDMPLab", L"2024-12-30");
    const IGenericElement* pGenericElement = quick_cast<IGenericElement>(GetOwnerElement());
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGenericElement, L"GenericElement不能为空", L"GDMPLab", L"2024-12-30");

    const IElementModelShape* pElementModelShape = pGenericElement->GetElementModelShape();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pElementModelShape, L"ElementModelShape不能为空", L"GDMPLab", L"2024-12-30");
    const IGraphicsElementShape* pGraphicsElementShape = pElementModelShape->GetGraphicsElementShape();
    IGraphicsStyle* pStyle = quick_cast<IGraphicsStyle>(pDocument->GetElement(pGraphicsElementShape->GetGraphicsStyleId()));
    DBG_WARN_AND_RETURN_VOID_UNLESS(pStyle, L"pStyle不能为空", L"GDMPLab", L"2024-12-30");
    OwnerPtr<IGraphicsStyleData> opGStyleData = pStyle->GetGraphicsStyleData();

    if (GetColorMode() == ColorMode::ByColor)
    {
        opGStyleData->SetColor(GetColor());
        opGStyleData->SetColorIndex(-1);
        opGStyleData->SetSmartColorMode(SmartColorMode::Invalid);
    }
    else if (GetColorMode() == ColorMode::ByColorIndex)
    {
        opGStyleData->SetColorIndex(GetColorIndex());

    }

    if (GetColorMode() == ColorMode::SmartUseBackgroundColor)
    { 
        opGStyleData->SetSmartColorMode(SmartColorMode::SmartUseBackgroundColor);
    }
    else if (GetColorMode() == ColorMode::SmartAntiBackgroundColor)
    {
        opGStyleData->SetSmartColorMode(SmartColorMode::SmartAntiBackgroundColor);
    }
    else
    {
        opGStyleData->SetSmartColorMode(SmartColorMode::Invalid);
    }
    pStyle->SetGraphicsStyleData(*opGStyleData);
}

IDocument * ParamSampleExternalObject::GetDocument() const
{
    if (!m_pOwnerElement)
    {
        return nullptr;
    }
    return m_pOwnerElement->GetDocument();
}

ElementId ParamSampleExternalObject::GetOwnerElementId() const
{
    if (!m_pOwnerElement)
    {
        return ElementId::InvalidID;
    }
    return m_pOwnerElement->GetElementId();
}

void ParamSampleExternalObject::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    ElementUtils::UpdateForWeakParentDeletion(this->GetOwnerElement(), deletedElementIds);
    if (deletedElementIds.count(GetMaterialId()) > 0)
    {
        SetMaterialId(ElementId::InvalidID);
    }
    if (deletedElementIds.count(GetViewId()) > 0)
    {
        SetViewId(ElementId::InvalidID);
    }
}

void ParamSampleExternalObject::ReportParents(IElementParentReporter& reporter) const
{
    ElementUtils::ReportParents(this->GetOwnerElement(), reporter);
    reporter.ReportWeak(GetMaterialId());
    reporter.ReportWeak(GetViewId());
}


