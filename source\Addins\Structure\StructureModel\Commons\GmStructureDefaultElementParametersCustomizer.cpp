﻿#include "GmStructureDefaultElementParametersCustomizer.h"
#include "DbObjectUtils.h"
#include "IDocument.h"
#include "IElement.h"
#include "IParameter.h"
#include "OwnerPtr.h"
#include "IInstance.h"
#include "IElementParameters.h"

#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;

void GmStructureDefaultElementParametersCustomizer::SetElementParametersCustomizer(IElement* pOwnerElement,
    OwnerPtr<IElementParametersCustomizer> opNewCustomizer)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(pOwnerElement && opNewCustomizer, L"invalid input arguments",L"GDMPLab",L"2024-03-30");

    IElementParameters* pElementParameters = pOwnerElement->GetElementParameters();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pElementParameters, L"element parameters is null.",L"GDMPLab",L"2024-03-30");

    OwnerPtr<IElementParametersCustomizer> opOldCustomizer = pElementParameters->SetElementParametersCustomizer(TransferOwnership(opNewCustomizer));
    if (opOldCustomizer)
    {
        GmStructureDefaultElementParametersCustomizer* pCustomizer = quick_cast<GmStructureDefaultElementParametersCustomizer>(
            pOwnerElement->GetElementParameters()->GetParametersCustomizer());
        DBG_WARN_AND_RETURN_VOID_UNLESS(pCustomizer, L"GmStructureDefaultElementParametersCustomizer is null.",L"GDMPLab",L"2024-03-30");
        pCustomizer->SetProxyCustomizer(TransferOwnership(opOldCustomizer));
    }
}

DBOBJECT_DATA_DEFINE(GmStructureDefaultElementParametersCustomizer)
{
    m_pOwnerElement = nullptr;
}

GmStructureDefaultElementParametersCustomizer::GmStructureDefaultElementParametersCustomizer(IElement* pElement) : m_pOwnerElement(pElement), m_ProxyCustomizer(nullptr)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(m_pOwnerElement, L"owner element is null.",L"GDMPLab",L"2024-03-30");
}

OwnerPtr<IParameter> GmStructureDefaultElementParametersCustomizer::GetNativeParameter(int paramDefId) const
{
    auto pDefaultInstanceCustomizer = IElementParametersCustomizer::CreateDefaultInstanceParametersCustomizer(m_pOwnerElement);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pDefaultInstanceCustomizer, L"proxy customizer is null.",L"GDMPLab",L"2024-03-30");
    return pDefaultInstanceCustomizer->GetNativeParameter(paramDefId);
}

bool GmStructureDefaultElementParametersCustomizer::SetNativeParameter(const IParameter* param, std::wstring* errorMsg)
{
    auto pDefaultInstanceCustomizer = IElementParametersCustomizer::CreateDefaultInstanceParametersCustomizer(m_pOwnerElement);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pDefaultInstanceCustomizer, L"proxy customizer is null.",L"GDMPLab",L"2024-03-30");
    return pDefaultInstanceCustomizer->SetNativeParameter(param, errorMsg);
}

void GmStructureDefaultElementParametersCustomizer::ReportParameterDefinitions(std::vector<int>* pParamDefIds) const
{
    auto pDefaultInstanceCustomizer = IElementParametersCustomizer::CreateDefaultInstanceParametersCustomizer(m_pOwnerElement);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDefaultInstanceCustomizer, L"proxy customizer is null.",L"GDMPLab",L"2024-03-30");
    pDefaultInstanceCustomizer->ReportParameterDefinitions(pParamDefIds);
}

bool GmStructureDefaultElementParametersCustomizer::IsParameterModifiable(int parameterId) const
{
    auto pDefaultInstanceCustomizer = IElementParametersCustomizer::CreateDefaultInstanceParametersCustomizer(m_pOwnerElement);
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pDefaultInstanceCustomizer, L"proxy customizer is null.",L"GDMPLab",L"2024-03-30");
    return pDefaultInstanceCustomizer->IsParameterModifiable(parameterId);
}

NdbObject* gcmp::GmStructureDefaultElementParametersCustomizer::GetTopOwnerObject() const
{
    return nullptr;
}

void gcmp::GmStructureDefaultElementParametersCustomizer::SetProxyCustomizer(OwnerPtr<IElementParametersCustomizer> opProxyCustomizer)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(opProxyCustomizer, L"invalid input argument.",L"GDMPLab",L"2024-03-30");
    m_ProxyCustomizer = TransferOwnership(opProxyCustomizer);
    m_ProxyCustomizer->SetOwnerElement(m_pOwnerElement);
}

