﻿#include "CmdParameterSample.h"
#include "ElemModuleCommandIds.h"
#include "CommandRegister.h"
#include "IDocument.h"
#include "IModelView.h"
#include "ICurve2d.h"
#include "ILine2d.h"
#include "IArc2d.h"
#include "IPolyCurve.h"
#include "IDrawingPolyCurve.h"
#include "UiDocumentViewUtils.h"
#include "IUserTransaction.h"
#include "IApplicationWindow.h"
#include "IApplication.h"
#include "ICommandManager.h"
#include "GcmpCommandNames.h"
#include "ParamSampleExternalObject.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

CmdParameterSample::CmdParameterSample()
    :CommandBase(ID_CMD_PARAMETER_SAMPLE, true)
{
}

gcmp::OwnerPtr<gcmp::IAction> CmdParameterSample::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pDoc, L"pDoc为空", L"GDMPLab", L"2024-12-30");

    OwnerPtr<IUserTransaction> opUserTrans = IUserTransaction::Create(pDoc, L"参数示例");
    ParamSampleExternalObject::Create(pDoc);

    std::vector<OwnerPtr<ICurve2d>> opCrvs;
    OwnerPtr<ILine2d> opLn = ILine2d::Create({ -500,500 }, { 500,500 });
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opLn, L"opLn为空", L"GDMPLab", L"2024-06-06");
    opCrvs.emplace_back(TransferOwnership(opLn));
    OwnerPtr<IArc2d> opArc = IArc2d::Create({ 500,500 }, { -500,500 }, { 0, 1000 });
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opArc, L"opArc为空", L"GDMPLab", L"2024-06-06");
    opCrvs.emplace_back(TransferOwnership(opArc));
    OwnerPtr<IPolyCurve> opPolyCrv = IPolyCurve::Create(TransferOwnership(opCrvs));
    gcmp::IDrawingPolyCurve* pDrawingPolyCurve = IDrawingPolyCurve::Create(
        pDoc,
        UiDocumentViewUtils::GetCurrentModelView(),
        TransferOwnership(opPolyCrv)
    );

    opUserTrans->Commit();
    return nullptr;
}

REGISTER_COMMAND(CmdParameterSample);
